/**
 * @file
 * 用户交互增强功能：收藏、分享、评论等
 */

(function (Drupal) {
  'use strict';

  /**
   * 用户交互行为
   */
  Drupal.behaviors.userInteractions = {
    attach: function (context, settings) {
      // 初始化收藏功能
      this.initFavorites(context);
      
      // 初始化分享功能
      this.initSocialShare(context);
      
      // 初始化图片查看器
      this.initImageViewer(context);
      
      // 初始化工具提示
      this.initTooltips(context);
      
      // 初始化回到顶部按钮
      this.initBackToTop(context);
    },

    /**
     * 初始化收藏功能
     */
    initFavorites: function (context) {
      const favoriteButtons = context.querySelectorAll('.favorite-btn:not(.processed)');
      
      favoriteButtons.forEach(button => {
        button.classList.add('processed');
        button.addEventListener('click', this.handleFavoriteClick.bind(this));
      });
    },

    /**
     * 处理收藏点击
     */
    handleFavoriteClick: function (event) {
      event.preventDefault();
      const button = event.currentTarget;
      const nodeId = button.getAttribute('data-node-id');
      const isFavorited = button.classList.contains('favorited');

      // 添加加载状态
      button.classList.add('loading');
      
      // 模拟API调用
      this.toggleFavorite(nodeId, !isFavorited)
        .then(result => {
          if (result.success) {
            if (isFavorited) {
              button.classList.remove('favorited');
              button.querySelector('.favorite-text').textContent = '收藏';
              this.showNotification('已取消收藏', 'info');
            } else {
              button.classList.add('favorited');
              button.querySelector('.favorite-text').textContent = '已收藏';
              this.showNotification('已添加到收藏', 'success');
            }
          }
        })
        .catch(error => {
          console.error('收藏操作失败:', error);
          this.showNotification('操作失败，请重试', 'error');
        })
        .finally(() => {
          button.classList.remove('loading');
        });
    },

    /**
     * 切换收藏状态（模拟API）
     */
    toggleFavorite: function (nodeId, favorite) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 这里应该调用实际的Drupal API
          const favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
          
          if (favorite) {
            if (!favorites.includes(nodeId)) {
              favorites.push(nodeId);
            }
          } else {
            const index = favorites.indexOf(nodeId);
            if (index > -1) {
              favorites.splice(index, 1);
            }
          }
          
          localStorage.setItem('favorites', JSON.stringify(favorites));
          resolve({ success: true });
        }, 500);
      });
    },

    /**
     * 初始化社交分享
     */
    initSocialShare: function (context) {
      const shareButtons = context.querySelectorAll('.share-btn:not(.processed)');
      
      shareButtons.forEach(button => {
        button.classList.add('processed');
        button.addEventListener('click', this.handleShareClick.bind(this));
      });

      // 初始化分享面板
      const sharePanels = context.querySelectorAll('.share-panel:not(.processed)');
      sharePanels.forEach(panel => {
        panel.classList.add('processed');
        this.initSharePanel(panel);
      });
    },

    /**
     * 处理分享按钮点击
     */
    handleShareClick: function (event) {
      event.preventDefault();
      const button = event.currentTarget;
      const sharePanel = button.nextElementSibling;
      
      if (sharePanel && sharePanel.classList.contains('share-panel')) {
        sharePanel.classList.toggle('hidden');
      }
    },

    /**
     * 初始化分享面板
     */
    initSharePanel: function (panel) {
      const currentUrl = encodeURIComponent(window.location.href);
      const currentTitle = encodeURIComponent(document.title);
      
      // 社交媒体分享链接
      const shareLinks = {
        weibo: `https://service.weibo.com/share/share.php?url=${currentUrl}&title=${currentTitle}`,
        wechat: '#', // 微信分享需要特殊处理
        qq: `https://connect.qq.com/widget/shareqq/index.html?url=${currentUrl}&title=${currentTitle}`,
        email: `mailto:?subject=${currentTitle}&body=${currentUrl}`
      };

      // 更新分享链接
      Object.keys(shareLinks).forEach(platform => {
        const link = panel.querySelector(`[data-share="${platform}"]`);
        if (link && platform !== 'wechat') {
          link.href = shareLinks[platform];
          link.target = '_blank';
        }
      });

      // 复制链接功能
      const copyButton = panel.querySelector('.copy-link-btn');
      if (copyButton) {
        copyButton.addEventListener('click', () => {
          navigator.clipboard.writeText(window.location.href)
            .then(() => {
              this.showNotification('链接已复制到剪贴板', 'success');
            })
            .catch(() => {
              this.showNotification('复制失败，请手动复制', 'error');
            });
        });
      }
    },

    /**
     * 初始化图片查看器
     */
    initImageViewer: function (context) {
      const images = context.querySelectorAll('.zoomable-image:not(.processed)');
      
      images.forEach(img => {
        img.classList.add('processed');
        img.addEventListener('click', this.openImageViewer.bind(this));
      });
    },

    /**
     * 打开图片查看器
     */
    openImageViewer: function (event) {
      const img = event.currentTarget;
      const src = img.src || img.getAttribute('data-src');
      const alt = img.alt || '图片';

      // 创建模态框
      const modal = document.createElement('div');
      modal.className = 'image-viewer-modal fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50';
      modal.innerHTML = `
        <div class="relative max-w-full max-h-full p-4">
          <img src="${src}" alt="${alt}" class="max-w-full max-h-full object-contain">
          <button class="absolute top-4 right-4 text-white hover:text-gray-300 text-2xl font-bold">
            ×
          </button>
        </div>
      `;

      // 添加关闭事件
      modal.addEventListener('click', (e) => {
        if (e.target === modal || e.target.tagName === 'BUTTON') {
          document.body.removeChild(modal);
          document.body.classList.remove('overflow-hidden');
        }
      });

      // ESC键关闭
      const handleEsc = (e) => {
        if (e.key === 'Escape') {
          document.body.removeChild(modal);
          document.body.classList.remove('overflow-hidden');
          document.removeEventListener('keydown', handleEsc);
        }
      };
      document.addEventListener('keydown', handleEsc);

      document.body.appendChild(modal);
      document.body.classList.add('overflow-hidden');
    },

    /**
     * 初始化工具提示
     */
    initTooltips: function (context) {
      const tooltipElements = context.querySelectorAll('[data-tooltip]:not(.processed)');
      
      tooltipElements.forEach(element => {
        element.classList.add('processed');
        
        let tooltip;
        
        element.addEventListener('mouseenter', () => {
          const text = element.getAttribute('data-tooltip');
          tooltip = this.createTooltip(text);
          document.body.appendChild(tooltip);
          this.positionTooltip(tooltip, element);
        });

        element.addEventListener('mouseleave', () => {
          if (tooltip) {
            document.body.removeChild(tooltip);
            tooltip = null;
          }
        });
      });
    },

    /**
     * 创建工具提示
     */
    createTooltip: function (text) {
      const tooltip = document.createElement('div');
      tooltip.className = 'tooltip absolute bg-gray-900 text-white text-sm px-2 py-1 rounded shadow-lg z-50 pointer-events-none';
      tooltip.textContent = text;
      return tooltip;
    },

    /**
     * 定位工具提示
     */
    positionTooltip: function (tooltip, element) {
      const rect = element.getBoundingClientRect();
      const tooltipRect = tooltip.getBoundingClientRect();
      
      let top = rect.top - tooltipRect.height - 8;
      let left = rect.left + (rect.width - tooltipRect.width) / 2;
      
      // 边界检查
      if (top < 0) {
        top = rect.bottom + 8;
      }
      if (left < 0) {
        left = 8;
      }
      if (left + tooltipRect.width > window.innerWidth) {
        left = window.innerWidth - tooltipRect.width - 8;
      }
      
      tooltip.style.top = top + window.scrollY + 'px';
      tooltip.style.left = left + 'px';
    },

    /**
     * 初始化回到顶部按钮
     */
    initBackToTop: function (context) {
      if (context !== document) return;

      // 创建回到顶部按钮
      const backToTop = document.createElement('button');
      backToTop.className = 'back-to-top fixed bottom-8 right-8 w-12 h-12 bg-primary-600 text-white rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 opacity-0 invisible';
      backToTop.innerHTML = `
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
      `;
      
      backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      });

      document.body.appendChild(backToTop);

      // 滚动显示/隐藏
      let ticking = false;
      const handleScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            if (window.scrollY > 300) {
              backToTop.classList.remove('opacity-0', 'invisible');
            } else {
              backToTop.classList.add('opacity-0', 'invisible');
            }
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
    },

    /**
     * 显示通知
     */
    showNotification: function (message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 ${this.getNotificationClass(type)}`;
      notification.innerHTML = `
        <div class="flex items-center">
          ${this.getNotificationIcon(type)}
          <span class="ml-2">${message}</span>
          <button class="ml-4 text-current opacity-70 hover:opacity-100">×</button>
        </div>
      `;

      // 添加关闭事件
      notification.querySelector('button').addEventListener('click', () => {
        this.hideNotification(notification);
      });

      document.body.appendChild(notification);

      // 显示动画
      setTimeout(() => {
        notification.classList.remove('translate-x-full');
      }, 100);

      // 自动隐藏
      setTimeout(() => {
        this.hideNotification(notification);
      }, 5000);
    },

    /**
     * 隐藏通知
     */
    hideNotification: function (notification) {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        if (notification.parentNode) {
          document.body.removeChild(notification);
        }
      }, 300);
    },

    /**
     * 获取通知样式类
     */
    getNotificationClass: function (type) {
      const classes = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-white',
        info: 'bg-blue-500 text-white'
      };
      return classes[type] || classes.info;
    },

    /**
     * 获取通知图标
     */
    getNotificationIcon: function (type) {
      const icons = {
        success: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
        error: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
        warning: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        info: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
      };
      return icons[type] || icons.info;
    }
  };

})(Drupal);
