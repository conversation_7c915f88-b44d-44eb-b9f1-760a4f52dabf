/**
 * @file
 * 图片懒加载和性能优化功能
 */

(function (Drupal) {
  'use strict';

  /**
   * 图片懒加载行为
   */
  Drupal.behaviors.lazyLoading = {
    attach: function (context, settings) {
      // 检查浏览器是否支持 Intersection Observer
      if ('IntersectionObserver' in window) {
        this.initIntersectionObserver(context);
      } else {
        // 降级处理：立即加载所有图片
        this.loadAllImages(context);
      }

      // 初始化其他性能优化功能
      this.initPerformanceOptimizations(context);
    },

    /**
     * 初始化 Intersection Observer 懒加载
     */
    initIntersectionObserver: function (context) {
      const images = context.querySelectorAll('img[data-src]:not(.lazy-loaded)');
      
      if (images.length === 0) return;

      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            this.loadImage(img);
            observer.unobserve(img);
          }
        });
      }, {
        // 提前50px开始加载
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      images.forEach(img => {
        imageObserver.observe(img);
      });
    },

    /**
     * 加载单个图片
     */
    loadImage: function (img) {
      const src = img.getAttribute('data-src');
      const srcset = img.getAttribute('data-srcset');
      
      if (!src) return;

      // 创建新的图片对象预加载
      const imageLoader = new Image();
      
      imageLoader.onload = () => {
        // 图片加载成功后更新src
        img.src = src;
        if (srcset) {
          img.srcset = srcset;
        }
        
        // 添加淡入动画
        img.classList.add('lazy-loaded');
        img.classList.remove('lazy-loading');
        
        // 移除data属性
        img.removeAttribute('data-src');
        img.removeAttribute('data-srcset');
      };

      imageLoader.onerror = () => {
        // 加载失败处理
        img.classList.add('lazy-error');
        img.classList.remove('lazy-loading');
      };

      // 开始加载
      img.classList.add('lazy-loading');
      imageLoader.src = src;
      if (srcset) {
        imageLoader.srcset = srcset;
      }
    },

    /**
     * 降级处理：加载所有图片
     */
    loadAllImages: function (context) {
      const images = context.querySelectorAll('img[data-src]');
      images.forEach(img => {
        this.loadImage(img);
      });
    },

    /**
     * 初始化性能优化功能
     */
    initPerformanceOptimizations: function (context) {
      // 预加载关键资源
      this.preloadCriticalResources();
      
      // 优化字体加载
      this.optimizeFontLoading();
      
      // 初始化滚动性能优化
      this.initScrollOptimization(context);
    },

    /**
     * 预加载关键资源
     */
    preloadCriticalResources: function () {
      // 预加载关键CSS
      const criticalCSS = document.querySelector('link[rel="stylesheet"][href*="tailwind"]');
      if (criticalCSS && !criticalCSS.hasAttribute('data-preloaded')) {
        const preloadLink = document.createElement('link');
        preloadLink.rel = 'preload';
        preloadLink.as = 'style';
        preloadLink.href = criticalCSS.href;
        document.head.appendChild(preloadLink);
        criticalCSS.setAttribute('data-preloaded', 'true');
      }

      // 预加载重要图片
      const heroImages = document.querySelectorAll('.hero img, .featured img');
      heroImages.forEach(img => {
        if (img.getAttribute('data-src')) {
          this.loadImage(img);
        }
      });
    },

    /**
     * 优化字体加载
     */
    optimizeFontLoading: function () {
      // 使用font-display: swap优化字体加载
      if ('fonts' in document) {
        document.fonts.ready.then(() => {
          document.body.classList.add('fonts-loaded');
        });
      }
    },

    /**
     * 滚动性能优化
     */
    initScrollOptimization: function (context) {
      let ticking = false;

      const optimizedScroll = () => {
        // 滚动时的性能优化逻辑
        this.updateScrollIndicators();
        ticking = false;
      };

      const onScroll = () => {
        if (!ticking) {
          requestAnimationFrame(optimizedScroll);
          ticking = true;
        }
      };

      // 使用passive事件监听器优化滚动性能
      window.addEventListener('scroll', onScroll, { passive: true });
    },

    /**
     * 更新滚动指示器
     */
    updateScrollIndicators: function () {
      const scrolled = window.pageYOffset;
      const rate = scrolled / (document.body.scrollHeight - window.innerHeight);
      
      // 更新进度条（如果存在）
      const progressBar = document.querySelector('.scroll-progress');
      if (progressBar) {
        progressBar.style.width = (rate * 100) + '%';
      }

      // 显示/隐藏回到顶部按钮
      const backToTop = document.querySelector('.back-to-top');
      if (backToTop) {
        if (scrolled > 300) {
          backToTop.classList.add('visible');
        } else {
          backToTop.classList.remove('visible');
        }
      }
    }
  };

  /**
   * 页面过渡效果
   */
  Drupal.behaviors.pageTransitions = {
    attach: function (context, settings) {
      // 为页面添加淡入效果
      if (context === document) {
        document.body.classList.add('page-loaded');
      }

      // 为链接添加平滑过渡
      const internalLinks = context.querySelectorAll('a[href^="/"], a[href^="' + window.location.origin + '"]');
      internalLinks.forEach(link => {
        if (!link.hasAttribute('data-transition-added')) {
          link.addEventListener('click', this.handleLinkClick.bind(this));
          link.setAttribute('data-transition-added', 'true');
        }
      });
    },

    handleLinkClick: function (event) {
      const link = event.currentTarget;
      const href = link.getAttribute('href');
      
      // 跳过特殊链接
      if (link.hasAttribute('download') || 
          link.getAttribute('target') === '_blank' ||
          href.includes('#') ||
          event.ctrlKey || event.metaKey) {
        return;
      }

      // 添加页面离开动画
      document.body.classList.add('page-leaving');
      
      // 延迟导航以显示动画
      setTimeout(() => {
        window.location.href = href;
      }, 150);
    }
  };

})(Drupal);
