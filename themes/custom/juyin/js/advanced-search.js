/**
 * @file
 * 高级搜索功能和AJAX自动完成
 */

(function (Drupal, drupalSettings) {
  'use strict';

  /**
   * 高级搜索行为
   */
  Drupal.behaviors.advancedSearch = {
    attach: function (context, settings) {
      const searchInput = context.querySelector('#search-input');
      const suggestionsContainer = context.querySelector('#search-suggestions');
      
      if (searchInput && !searchInput.hasAttribute('data-search-enhanced')) {
        this.initAdvancedSearch(searchInput, suggestionsContainer);
        searchInput.setAttribute('data-search-enhanced', 'true');
      }

      // 初始化搜索结果高亮
      this.initSearchHighlight(context);
    },

    /**
     * 初始化高级搜索功能
     */
    initAdvancedSearch: function (searchInput, suggestionsContainer) {
      let searchTimeout;
      let currentRequest;
      
      // 搜索建议数据缓存
      const searchCache = new Map();
      
      // 输入事件处理
      searchInput.addEventListener('input', (event) => {
        const query = event.target.value.trim();
        
        // 清除之前的定时器
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
          this.hideSuggestions(suggestionsContainer);
          return;
        }

        // 防抖处理，300ms后执行搜索
        searchTimeout = setTimeout(() => {
          this.performSearch(query, suggestionsContainer, searchCache);
        }, 300);
      });

      // 键盘导航
      searchInput.addEventListener('keydown', (event) => {
        this.handleKeyboardNavigation(event, suggestionsContainer);
      });

      // 点击外部隐藏建议
      document.addEventListener('click', (event) => {
        if (!searchInput.contains(event.target) && 
            !suggestionsContainer.contains(event.target)) {
          this.hideSuggestions(suggestionsContainer);
        }
      });
    },

    /**
     * 执行搜索请求
     */
    performSearch: function (query, suggestionsContainer, searchCache) {
      // 检查缓存
      if (searchCache.has(query)) {
        this.displaySuggestions(searchCache.get(query), suggestionsContainer, query);
        return;
      }

      // 显示加载状态
      this.showLoadingState(suggestionsContainer);

      // 模拟AJAX请求（实际项目中应该调用Drupal的AJAX API）
      this.mockSearchRequest(query)
        .then(results => {
          // 缓存结果
          searchCache.set(query, results);
          this.displaySuggestions(results, suggestionsContainer, query);
        })
        .catch(error => {
          console.error('搜索请求失败:', error);
          this.hideLoadingState(suggestionsContainer);
        });
    },

    /**
     * 模拟搜索请求（实际项目中替换为真实API调用）
     */
    mockSearchRequest: function (query) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockResults = [
            {
              type: 'brand',
              title: 'Cassina',
              description: '意大利顶级家具品牌',
              url: '/node/7',
              image: '/themes/custom/juyin/images/cassina-logo.jpg'
            },
            {
              type: 'product',
              title: 'LC2 扶手椅',
              description: 'Le Corbusier设计的经典扶手椅',
              url: '/node/12',
              image: '/themes/custom/juyin/images/lc2-chair.jpg'
            },
            {
              type: 'designer',
              title: 'Patricia Urquiola',
              description: '西班牙著名工业设计师',
              url: '/node/17',
              image: '/themes/custom/juyin/images/patricia-urquiola.jpg'
            },
            {
              type: 'news',
              title: '2024米兰家具展亮点',
              description: '探索今年米兰家具展的最新趋势',
              url: '/node/22',
              image: '/themes/custom/juyin/images/milan-furniture-fair.jpg'
            }
          ].filter(item => 
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.description.toLowerCase().includes(query.toLowerCase())
          );

          resolve(mockResults);
        }, 200);
      });
    },

    /**
     * 显示搜索建议
     */
    displaySuggestions: function (results, container, query) {
      this.hideLoadingState(container);
      
      if (results.length === 0) {
        container.innerHTML = `
          <div class="p-4 text-center text-gray-500">
            <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
            </svg>
            <p>未找到相关结果</p>
          </div>
        `;
        container.classList.remove('hidden');
        return;
      }

      const suggestionsHTML = results.map((item, index) => `
        <div class="suggestion-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0" 
             data-index="${index}" data-url="${item.url}">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                ${this.getTypeIcon(item.type)}
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                ${this.highlightQuery(item.title, query)}
              </p>
              <p class="text-sm text-gray-500 truncate">
                ${this.highlightQuery(item.description, query)}
              </p>
            </div>
            <div class="flex-shrink-0">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${this.getTypeBadgeClass(item.type)}">
                ${this.getTypeLabel(item.type)}
              </span>
            </div>
          </div>
        </div>
      `).join('');

      container.innerHTML = `
        <div class="suggestions-list">
          ${suggestionsHTML}
        </div>
        <div class="p-3 bg-gray-50 border-t border-gray-200">
          <button class="text-sm text-primary-600 hover:text-primary-700 font-medium">
            查看所有 "${query}" 的搜索结果 →
          </button>
        </div>
      `;

      // 添加点击事件
      container.querySelectorAll('.suggestion-item').forEach(item => {
        item.addEventListener('click', () => {
          window.location.href = item.getAttribute('data-url');
        });
      });

      container.classList.remove('hidden');
    },

    /**
     * 获取类型图标
     */
    getTypeIcon: function (type) {
      const icons = {
        brand: '<svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
        product: '<svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>',
        designer: '<svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20"><path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"></path></svg>',
        news: '<svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path></svg>'
      };
      return icons[type] || icons.product;
    },

    /**
     * 获取类型标签
     */
    getTypeLabel: function (type) {
      const labels = {
        brand: '品牌',
        product: '产品',
        designer: '设计师',
        news: '资讯'
      };
      return labels[type] || '内容';
    },

    /**
     * 获取类型徽章样式
     */
    getTypeBadgeClass: function (type) {
      const classes = {
        brand: 'bg-blue-100 text-blue-800',
        product: 'bg-green-100 text-green-800',
        designer: 'bg-purple-100 text-purple-800',
        news: 'bg-orange-100 text-orange-800'
      };
      return classes[type] || 'bg-gray-100 text-gray-800';
    },

    /**
     * 高亮查询词
     */
    highlightQuery: function (text, query) {
      if (!query) return text;
      const regex = new RegExp(`(${query})`, 'gi');
      return text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
    },

    /**
     * 显示加载状态
     */
    showLoadingState: function (container) {
      container.innerHTML = `
        <div class="p-4 text-center">
          <div class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm text-gray-600">搜索中...</span>
          </div>
        </div>
      `;
      container.classList.remove('hidden');
    },

    /**
     * 隐藏加载状态
     */
    hideLoadingState: function (container) {
      // 加载状态会被新内容替换，这里不需要特殊处理
    },

    /**
     * 隐藏建议
     */
    hideSuggestions: function (container) {
      if (container) {
        container.classList.add('hidden');
      }
    },

    /**
     * 键盘导航处理
     */
    handleKeyboardNavigation: function (event, container) {
      const suggestions = container.querySelectorAll('.suggestion-item');
      if (suggestions.length === 0) return;

      let currentIndex = -1;
      const currentActive = container.querySelector('.suggestion-item.active');
      if (currentActive) {
        currentIndex = parseInt(currentActive.getAttribute('data-index'));
      }

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          currentIndex = Math.min(currentIndex + 1, suggestions.length - 1);
          this.setActiveSuggestion(suggestions, currentIndex);
          break;
        case 'ArrowUp':
          event.preventDefault();
          currentIndex = Math.max(currentIndex - 1, -1);
          this.setActiveSuggestion(suggestions, currentIndex);
          break;
        case 'Enter':
          event.preventDefault();
          if (currentIndex >= 0) {
            suggestions[currentIndex].click();
          }
          break;
        case 'Escape':
          this.hideSuggestions(container);
          break;
      }
    },

    /**
     * 设置活动建议项
     */
    setActiveSuggestion: function (suggestions, index) {
      suggestions.forEach((item, i) => {
        if (i === index) {
          item.classList.add('active', 'bg-primary-50');
        } else {
          item.classList.remove('active', 'bg-primary-50');
        }
      });
    },

    /**
     * 初始化搜索结果高亮
     */
    initSearchHighlight: function (context) {
      const searchResults = context.querySelectorAll('.search-result');
      const urlParams = new URLSearchParams(window.location.search);
      const searchQuery = urlParams.get('keys') || urlParams.get('search');

      if (searchQuery && searchResults.length > 0) {
        searchResults.forEach(result => {
          this.highlightSearchTerms(result, searchQuery);
        });
      }
    },

    /**
     * 高亮搜索词
     */
    highlightSearchTerms: function (element, query) {
      const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      const textNodes = [];
      let node;
      while (node = walker.nextNode()) {
        textNodes.push(node);
      }

      textNodes.forEach(textNode => {
        const text = textNode.textContent;
        const regex = new RegExp(`(${query})`, 'gi');
        if (regex.test(text)) {
          const highlightedHTML = text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded font-medium">$1</mark>');
          const wrapper = document.createElement('span');
          wrapper.innerHTML = highlightedHTML;
          textNode.parentNode.replaceChild(wrapper, textNode);
        }
      });
    }
  };

})(Drupal, drupalSettings);
