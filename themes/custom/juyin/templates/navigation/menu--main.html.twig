{#
/**
 * @file
 * Theme implementation for the main navigation menu.
 */
#}
{% import _self as menus %}

{#
  We call a macro which calls itself to render the full tree.
  @see https://twig.symfony.com/doc/1.x/tags/macro.html
#}
{{ menus.menu_links(items, attributes, 0) }}

{% macro menu_links(items, attributes, menu_level) %}
  {% import _self as menus %}
  {% if items %}
    {% if menu_level == 0 %}
      <ul{{ attributes.addClass('main-navigation', 'flex', 'items-baseline', 'space-x-8') }}>
    {% else %}
      <ul class="submenu absolute left-0 top-full mt-1 w-48 bg-white shadow-lg rounded-md py-2 z-50 hidden group-hover:block">
    {% endif %}
    {% for item in items %}
      {% set item_classes = [
        'menu-item',
        item.is_expanded ? 'menu-item--expanded',
        item.is_collapsed ? 'menu-item--collapsed',
        item.in_active_trail ? 'menu-item--active-trail',
        menu_level == 0 ? 'relative group' : ''
      ] %}
      <li{{ item.attributes.addClass(item_classes) }}>
        {% if menu_level == 0 %}
          {# Top level menu items #}
          {% set link_classes = [
            'text-gray-500',
            'hover:text-primary-600',
            'px-3',
            'py-2',
            'text-sm',
            'font-medium',
            'transition-colors',
            'duration-200',
            item.in_active_trail ? 'text-gray-900' : ''
          ] %}
          {{ link(item.title, item.url, {'class': link_classes}) }}
          
          {# Dropdown indicator for items with children #}
          {% if item.below %}
            <svg class="inline-block w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          {% endif %}
        {% else %}
          {# Submenu items #}
          {% set link_classes = [
            'block',
            'px-4',
            'py-2',
            'text-sm',
            'text-gray-700',
            'hover:bg-gray-100',
            'hover:text-primary-600',
            'transition-colors',
            'duration-200'
          ] %}
          {{ link(item.title, item.url, {'class': link_classes}) }}
        {% endif %}
        
        {% if item.below %}
          {{ menus.menu_links(item.below, attributes, menu_level + 1) }}
        {% endif %}
      </li>
    {% endfor %}
    </ul>
  {% endif %}
{% endmacro %}
