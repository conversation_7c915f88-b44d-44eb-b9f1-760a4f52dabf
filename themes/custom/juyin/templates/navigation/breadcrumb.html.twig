{#
/**
 * @file
 * Theme implementation for a breadcrumb trail.
 */
#}
{% if breadcrumb %}
  <nav class="breadcrumb-navigation" role="navigation" aria-labelledby="system-breadcrumb">
    <h2 id="system-breadcrumb" class="visually-hidden">{{ 'You are here'|t }}</h2>
    <ol class="breadcrumb flex items-center space-x-2 text-sm text-gray-500">
      {% for item in breadcrumb %}
        <li class="breadcrumb-item flex items-center">
          {% if item.url %}
            <a href="{{ item.url }}" class="text-gray-500 hover:text-primary-600 transition-colors duration-200">
              {{ item.text }}
            </a>
          {% else %}
            <span class="text-gray-900 font-medium">{{ item.text }}</span>
          {% endif %}
          
          {% if not loop.last %}
            <svg class="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          {% endif %}
        </li>
      {% endfor %}
    </ol>
  </nav>
{% endif %}
