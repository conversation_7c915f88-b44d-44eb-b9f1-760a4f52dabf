{#
/**
 * @file
 * Theme implementation to display a node.
 */
#}
<article{{ attributes.addClass('node', 'node--type-' ~ node.bundle|clean_class, node.isPromoted() ? 'node--promoted', node.isSticky() ? 'node--sticky', not node.isPublished() ? 'node--unpublished') }}>

  {# Node Header #}
  <header class="node-header mb-6">
    {% if display_submitted %}
      <div class="node-meta flex items-center justify-between mb-4">
        <div class="flex items-center space-x-4 text-sm text-gray-500">
          <time datetime="{{ node.getCreatedTime|date('c') }}" class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            {{ node.getCreatedTime|date('Y年m月d日') }}
          </time>
          
          {% if author_name %}
            <span class="flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              {{ author_name }}
            </span>
          {% endif %}
          
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
            </svg>
            {{ node.bundle|title }}
          </span>
        </div>

        {# Action Buttons #}
        <div class="flex items-center space-x-2">
          {# Favorite Button #}
          <button class="favorite-btn flex items-center px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200" 
                  data-node-id="{{ node.id() }}" data-tooltip="收藏此内容">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
            <span class="favorite-text">收藏</span>
          </button>

          {# Share Button #}
          <div class="relative">
            <button class="share-btn flex items-center px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200" 
                    data-tooltip="分享此内容">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
              </svg>
              <span>分享</span>
            </button>

            {# Share Panel #}
            <div class="share-panel absolute top-full right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 hidden">
              <div class="p-3">
                <h4 class="text-sm font-medium text-gray-900 mb-3">分享到</h4>
                <div class="grid grid-cols-2 gap-2">
                  <a href="#" data-share="weibo" class="flex items-center p-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                    <svg class="w-4 h-4 mr-2 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9.586 20.414a2 2 0 002.828 0l6.586-6.586a2 2 0 000-2.828L12.414 4.414a2 2 0 00-2.828 0L3 11l6.586 9.414z"/>
                    </svg>
                    微博
                  </a>
                  <a href="#" data-share="wechat" class="flex items-center p-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                    <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                    </svg>
                    微信
                  </a>
                  <a href="#" data-share="qq" class="flex items-center p-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                    </svg>
                    QQ
                  </a>
                  <button class="copy-link-btn flex items-center p-2 text-sm text-gray-700 hover:bg-gray-100 rounded">
                    <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                    复制链接
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}

    {# Title #}
    {{ title_prefix }}
    {% if label and not page %}
      <h2{{ title_attributes.addClass('node-title', 'text-2xl', 'font-bold', 'text-gray-900', 'mb-4') }}>
        <a href="{{ url }}" rel="bookmark" class="hover:text-primary-600 transition-colors duration-200">{{ label }}</a>
      </h2>
    {% elseif label and page %}
      <h1{{ title_attributes.addClass('node-title', 'text-3xl', 'font-bold', 'text-gray-900', 'mb-6') }}>
        {{ label }}
      </h1>
    {% endif %}
    {{ title_suffix }}
  </header>

  {# Node Content #}
  <div{{ content_attributes.addClass('node-content') }}>
    {% if content.field_image %}
      <div class="node-image mb-6">
        {{ content.field_image }}
      </div>
    {% endif %}

    {# Main Content #}
    <div class="prose prose-lg max-w-none">
      {{ content|without('field_image', 'links') }}
    </div>

    {# Tags/Categories #}
    {% if content.field_tags %}
      <div class="node-tags mt-6 pt-6 border-t border-gray-200">
        <h3 class="text-sm font-medium text-gray-900 mb-3">标签</h3>
        <div class="flex flex-wrap gap-2">
          {{ content.field_tags }}
        </div>
      </div>
    {% endif %}

    {# Links #}
    {% if content.links %}
      <div class="node-links mt-6 pt-6 border-t border-gray-200">
        {{ content.links }}
      </div>
    {% endif %}
  </div>

  {# Related Content #}
  {% if node.bundle == 'product' or node.bundle == 'brand' %}
    <div class="related-content mt-12 pt-8 border-t border-gray-200">
      <h3 class="text-xl font-semibold text-gray-900 mb-6">相关内容</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {# Related content will be populated by a view or custom logic #}
        <div class="bg-gray-100 rounded-lg p-6 text-center text-gray-500">
          <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
          <p>相关内容即将推出</p>
        </div>
      </div>
    </div>
  {% endif %}

</article>
