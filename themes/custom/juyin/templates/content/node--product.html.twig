{#
/**
 * @file
 * Theme implementation to display a product node - Optimized with Components
 */
#}

{# 准备产品数据对象 #}
{% set product_data = {
  id: node.id(),
  title: label,
  url: url,
  image: content.field_product_images,
  brand: content.field_product_brand['#items'].0.value|default(''),
  category: content.field_product_category['#items'].0.value|default(''),
  description: content.field_product_description['#items'].0.value|default(''),
  price: content.field_product_price['#items'].0.value|default(''),
  original_price: content.field_original_price['#items'].0.value|default(''),
  rating: content.field_product_rating['#items'].0.value|default(0)
} %}

{# 根据显示模式选择卡片变体 #}
{% set card_variant = view_mode == 'teaser' ? 'compact' : (node.isPromoted() ? 'featured' : 'default') %}

{# 使用产品卡片组件 #}
{% include '@juyin/components/cards/product-card.html.twig' with {
  'product': product_data,
  'variant': card_variant,
  'show_actions': true,
  'show_rating': true,
  'show_description': (view_mode != 'teaser')
} %}
