{#
/**
 * @file
 * 通用按钮组件 - 基于Tailwind CSS的可配置按钮
 * 
 * 可用参数:
 * - text: 按钮文本
 * - url: 链接地址（如果是链接按钮）
 * - type: 按钮类型 ('button', 'submit', 'reset')
 * - variant: 按钮变体 ('primary', 'secondary', 'outline', 'ghost', 'danger')
 * - size: 按钮大小 ('xs', 'sm', 'md', 'lg', 'xl')
 * - icon: 图标名称（可选）
 * - icon_position: 图标位置 ('left', 'right', 'only')
 * - disabled: 是否禁用
 * - loading: 是否显示加载状态
 * - full_width: 是否全宽
 * - attributes: 额外的HTML属性
 * - classes: 额外的CSS类
 */
#}

{# 设置默认值 #}
{% set text = text|default('按钮') %}
{% set type = type|default('button') %}
{% set variant = variant|default('primary') %}
{% set size = size|default('md') %}
{% set icon_position = icon_position|default('left') %}
{% set disabled = disabled|default(false) %}
{% set loading = loading|default(false) %}
{% set full_width = full_width|default(false) %}

{# 基础样式类 #}
{% set base_classes = 'inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed' %}

{# 变体样式类 #}
{% set variant_classes = {
  'primary': 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm hover:shadow-md',
  'secondary': 'bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500 shadow-sm hover:shadow-md',
  'outline': 'border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500 hover:border-gray-400',
  'ghost': 'text-gray-700 hover:bg-gray-100 focus:ring-primary-500',
  'danger': 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md'
} %}

{# 大小样式类 #}
{% set size_classes = {
  'xs': 'px-2 py-1 text-xs',
  'sm': 'px-3 py-1.5 text-sm',
  'md': 'px-4 py-2 text-sm',
  'lg': 'px-6 py-3 text-base',
  'xl': 'px-8 py-4 text-lg'
} %}

{# 图标大小 #}
{% set icon_sizes = {
  'xs': 'w-3 h-3',
  'sm': 'w-4 h-4',
  'md': 'w-4 h-4',
  'lg': 'w-5 h-5',
  'xl': 'w-6 h-6'
} %}

{# 组合所有样式类 #}
{% set all_classes = [
  base_classes,
  variant_classes[variant],
  size_classes[size],
  full_width ? 'w-full' : '',
  loading ? 'relative' : '',
  classes|default('')
]|join(' ')|trim %}

{# 图标SVG映射 #}
{% set icons = {
  'search': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>',
  'heart': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>',
  'share': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>',
  'eye': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>',
  'plus': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>',
  'minus': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>',
  'check': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>',
  'x': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>',
  'arrow-right': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>',
  'arrow-left': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"/>',
  'download': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>',
  'upload': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>'
} %}

{# 渲染按钮或链接 #}
{% if url %}
  <a href="{{ url }}" 
     class="{{ all_classes }}"
     {% if disabled %}aria-disabled="true" tabindex="-1"{% endif %}
     {{ attributes|default({})|without('class') }}>
{% else %}
  <button type="{{ type }}" 
          class="{{ all_classes }}"
          {% if disabled or loading %}disabled{% endif %}
          {{ attributes|default({})|without('class') }}>
{% endif %}

  {# 加载状态指示器 #}
  {% if loading %}
    <div class="absolute inset-0 flex items-center justify-center">
      <svg class="animate-spin {{ icon_sizes[size] }} text-current" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  {% endif %}

  {# 按钮内容 #}
  <span class="{{ loading ? 'opacity-0' : '' }} flex items-center">
    
    {# 左侧图标 #}
    {% if icon and icon_position == 'left' and icon_position != 'only' %}
      <svg class="{{ icon_sizes[size] }} mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        {{ icons[icon]|raw }}
      </svg>
    {% endif %}

    {# 仅图标模式 #}
    {% if icon and icon_position == 'only' %}
      <svg class="{{ icon_sizes[size] }}" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        {{ icons[icon]|raw }}
      </svg>
      <span class="sr-only">{{ text }}</span>
    {% else %}
      {# 按钮文本 #}
      {{ text }}
    {% endif %}

    {# 右侧图标 #}
    {% if icon and icon_position == 'right' %}
      <svg class="{{ icon_sizes[size] }} ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        {{ icons[icon]|raw }}
      </svg>
    {% endif %}
  </span>

{% if url %}
  </a>
{% else %}
  </button>
{% endif %}
