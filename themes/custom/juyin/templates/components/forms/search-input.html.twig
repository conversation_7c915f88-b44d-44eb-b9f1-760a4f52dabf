{#
/**
 * @file
 * 搜索输入框组件 - 基于Tailwind CSS的高级搜索组件
 * 
 * 可用参数:
 * - placeholder: 占位符文本
 * - value: 初始值
 * - size: 输入框大小 ('sm', 'md', 'lg')
 * - variant: 样式变体 ('default', 'minimal', 'rounded')
 * - show_suggestions: 是否显示搜索建议 (默认: true)
 * - show_icon: 是否显示搜索图标 (默认: true)
 * - show_clear: 是否显示清除按钮 (默认: true)
 * - autocomplete: 是否启用自动完成
 * - debounce: 防抖延迟时间（毫秒）
 * - min_chars: 最小搜索字符数
 * - attributes: 额外的HTML属性
 * - classes: 额外的CSS类
 */
#}

{# 设置默认值 #}
{% set placeholder = placeholder|default('搜索产品、品牌、设计师...') %}
{% set size = size|default('md') %}
{% set variant = variant|default('default') %}
{% set show_suggestions = show_suggestions|default(true) %}
{% set show_icon = show_icon|default(true) %}
{% set show_clear = show_clear|default(true) %}
{% set autocomplete = autocomplete|default(true) %}
{% set debounce = debounce|default(300) %}
{% set min_chars = min_chars|default(2) %}

{# 生成唯一ID #}
{% set search_id = 'search-' ~ random() %}
{% set suggestions_id = search_id ~ '-suggestions' %}

{# 基础样式类 #}
{% set base_classes = 'w-full border border-gray-300 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200' %}

{# 变体样式类 #}
{% set variant_classes = {
  'default': 'rounded-lg',
  'minimal': 'rounded-md border-gray-200',
  'rounded': 'rounded-full'
} %}

{# 大小样式类 #}
{% set size_classes = {
  'sm': 'px-3 py-2 text-sm',
  'md': 'px-4 py-2 text-sm',
  'lg': 'px-6 py-3 text-base'
} %}

{# 图标大小 #}
{% set icon_sizes = {
  'sm': 'w-4 h-4',
  'md': 'w-5 h-5',
  'lg': 'w-6 h-6'
} %}

{# 图标位置 #}
{% set icon_positions = {
  'sm': 'left-3',
  'md': 'left-3',
  'lg': 'left-4'
} %}

{# 输入框左边距（为图标留空间） #}
{% set input_padding = {
  'sm': show_icon ? 'pl-9' : 'pl-3',
  'md': show_icon ? 'pl-10' : 'pl-4',
  'lg': show_icon ? 'pl-12' : 'pl-6'
} %}

{# 清除按钮位置 #}
{% set clear_positions = {
  'sm': 'right-3',
  'md': 'right-3',
  'lg': 'right-4'
} %}

{# 组合所有样式类 #}
{% set input_classes = [
  base_classes,
  variant_classes[variant],
  size_classes[size],
  input_padding[size],
  show_clear ? 'pr-10' : '',
  classes|default('')
]|join(' ')|trim %}

<div class="relative" data-search-component>
  
  {# 搜索输入框 #}
  <div class="relative">
    
    {# 搜索图标 #}
    {% if show_icon %}
      <div class="absolute inset-y-0 {{ icon_positions[size] }} flex items-center pointer-events-none">
        <svg class="{{ icon_sizes[size] }} text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
      </div>
    {% endif %}

    {# 输入框 #}
    <input type="search" 
           id="{{ search_id }}"
           name="search"
           class="{{ input_classes }}"
           placeholder="{{ placeholder }}"
           {% if value %}value="{{ value }}"{% endif %}
           {% if autocomplete %}autocomplete="off"{% endif %}
           aria-label="搜索"
           {% if show_suggestions %}
           aria-expanded="false"
           aria-haspopup="listbox"
           aria-owns="{{ suggestions_id }}"
           {% endif %}
           data-search-input
           data-debounce="{{ debounce }}"
           data-min-chars="{{ min_chars }}"
           {{ attributes|default({})|without('class') }}>

    {# 清除按钮 #}
    {% if show_clear %}
      <button type="button" 
              class="absolute inset-y-0 {{ clear_positions[size] }} flex items-center opacity-0 transition-opacity duration-200 hover:text-gray-600"
              aria-label="清除搜索"
              data-search-clear>
        <svg class="{{ icon_sizes[size] }}" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    {% endif %}

    {# 加载指示器 #}
    <div class="absolute inset-y-0 {{ clear_positions[size] }} flex items-center opacity-0 transition-opacity duration-200" 
         data-search-loading>
      <svg class="{{ icon_sizes[size] }} text-primary-500 animate-spin" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>
  </div>

  {# 搜索建议下拉框 #}
  {% if show_suggestions %}
    <div id="{{ suggestions_id }}" 
         class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 hidden max-h-96 overflow-y-auto"
         role="listbox"
         aria-label="搜索建议"
         data-search-suggestions>
      
      {# 建议列表容器 #}
      <div class="py-1" data-suggestions-list>
        {# 建议项将由JavaScript动态填充 #}
      </div>

      {# 无结果提示 #}
      <div class="px-4 py-3 text-sm text-gray-500 text-center hidden" data-no-results>
        <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
        <p>未找到相关结果</p>
        <p class="text-xs mt-1">尝试使用其他关键词</p>
      </div>

      {# 热门搜索 #}
      <div class="border-t border-gray-100 px-4 py-3 hidden" data-popular-searches>
        <h4 class="text-xs font-medium text-gray-900 mb-2">热门搜索</h4>
        <div class="flex flex-wrap gap-1">
          <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200" 
                  data-popular-term="沙发">
            沙发
          </button>
          <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200" 
                  data-popular-term="餐桌">
            餐桌
          </button>
          <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200" 
                  data-popular-term="床">
            床
          </button>
          <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors duration-200" 
                  data-popular-term="衣柜">
            衣柜
          </button>
        </div>
      </div>

      {# 搜索历史 #}
      <div class="border-t border-gray-100 px-4 py-3 hidden" data-search-history>
        <div class="flex items-center justify-between mb-2">
          <h4 class="text-xs font-medium text-gray-900">搜索历史</h4>
          <button class="text-xs text-gray-500 hover:text-gray-700" data-clear-history>
            清除
          </button>
        </div>
        <div class="space-y-1" data-history-list>
          {# 历史记录将由JavaScript动态填充 #}
        </div>
      </div>
    </div>
  {% endif %}
</div>

{# 搜索组件JavaScript #}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const searchComponent = document.querySelector('[data-search-component]');
  if (!searchComponent) return;

  const searchInput = searchComponent.querySelector('[data-search-input]');
  const searchClear = searchComponent.querySelector('[data-search-clear]');
  const searchLoading = searchComponent.querySelector('[data-search-loading]');
  const suggestions = searchComponent.querySelector('[data-search-suggestions]');
  const suggestionsList = searchComponent.querySelector('[data-suggestions-list]');
  const noResults = searchComponent.querySelector('[data-no-results]');
  const popularSearches = searchComponent.querySelector('[data-popular-searches]');
  const searchHistory = searchComponent.querySelector('[data-search-history]');

  let debounceTimer;
  let currentQuery = '';

  // 防抖搜索
  function debounceSearch(callback, delay) {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(callback, delay);
  }

  // 显示/隐藏清除按钮
  function toggleClearButton() {
    if (searchClear) {
      searchClear.style.opacity = searchInput.value ? '1' : '0';
    }
  }

  // 显示/隐藏加载指示器
  function toggleLoading(show) {
    if (searchLoading) {
      searchLoading.style.opacity = show ? '1' : '0';
    }
  }

  // 显示/隐藏建议框
  function toggleSuggestions(show) {
    if (suggestions) {
      suggestions.classList.toggle('hidden', !show);
      searchInput.setAttribute('aria-expanded', show);
    }
  }

  // 执行搜索
  function performSearch(query) {
    if (query.length < parseInt(searchInput.dataset.minChars)) {
      toggleSuggestions(false);
      return;
    }

    toggleLoading(true);
    currentQuery = query;

    // 模拟API调用
    setTimeout(() => {
      toggleLoading(false);
      
      // 这里应该是实际的搜索API调用
      const mockResults = [
        { type: 'product', title: '意大利真皮沙发', category: '沙发' },
        { type: 'brand', title: 'Natuzzi', category: '品牌' },
        { type: 'designer', title: 'Antonio Citterio', category: '设计师' }
      ];

      displaySuggestions(mockResults);
    }, 500);
  }

  // 显示搜索建议
  function displaySuggestions(results) {
    if (!suggestionsList) return;

    suggestionsList.innerHTML = '';
    
    if (results.length === 0) {
      noResults.classList.remove('hidden');
      popularSearches.classList.remove('hidden');
    } else {
      noResults.classList.add('hidden');
      popularSearches.classList.add('hidden');
      
      results.forEach((result, index) => {
        const item = document.createElement('button');
        item.className = 'w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none';
        item.innerHTML = `
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
            <div>
              <div class="text-sm font-medium text-gray-900">${result.title}</div>
              <div class="text-xs text-gray-500">${result.category}</div>
            </div>
          </div>
        `;
        item.addEventListener('click', () => {
          searchInput.value = result.title;
          toggleSuggestions(false);
          // 执行搜索跳转
          window.location.href = `/search?q=${encodeURIComponent(result.title)}`;
        });
        suggestionsList.appendChild(item);
      });
    }

    toggleSuggestions(true);
  }

  // 事件监听器
  searchInput.addEventListener('input', function() {
    toggleClearButton();
    const query = this.value.trim();
    
    if (query) {
      debounceSearch(() => performSearch(query), parseInt(this.dataset.debounce));
    } else {
      toggleSuggestions(false);
    }
  });

  searchInput.addEventListener('focus', function() {
    if (this.value) {
      performSearch(this.value);
    } else {
      // 显示热门搜索和历史记录
      if (popularSearches) popularSearches.classList.remove('hidden');
      if (searchHistory) searchHistory.classList.remove('hidden');
      toggleSuggestions(true);
    }
  });

  if (searchClear) {
    searchClear.addEventListener('click', function() {
      searchInput.value = '';
      searchInput.focus();
      toggleClearButton();
      toggleSuggestions(false);
    });
  }

  // 点击外部关闭建议框
  document.addEventListener('click', function(event) {
    if (!searchComponent.contains(event.target)) {
      toggleSuggestions(false);
    }
  });

  // 键盘导航
  searchInput.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
      toggleSuggestions(false);
    }
  });

  // 初始化
  toggleClearButton();
});
</script>
