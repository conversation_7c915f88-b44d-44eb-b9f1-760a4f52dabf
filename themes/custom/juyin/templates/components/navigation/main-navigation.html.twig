{#
/**
 * @file
 * 主导航组件 - 基于Tailwind CSS的响应式导航
 * 
 * 可用参数:
 * - logo_text: 品牌名称 (默认: "Juyin")
 * - logo_subtitle: 品牌副标题 (默认: "意大利家居")
 * - menu_items: 菜单项数组
 * - search_placeholder: 搜索框占位符
 * - show_search: 是否显示搜索框 (默认: true)
 * - show_user_menu: 是否显示用户菜单 (默认: true)
 * - show_language_switcher: 是否显示语言切换 (默认: true)
 */
#}

{# 设置默认值 #}
{% set logo_text = logo_text|default('Juyin') %}
{% set logo_subtitle = logo_subtitle|default('意大利家居') %}
{% set search_placeholder = search_placeholder|default('搜索产品、品牌、设计师...') %}
{% set show_search = show_search|default(true) %}
{% set show_user_menu = show_user_menu|default(true) %}
{% set show_language_switcher = show_language_switcher|default(true) %}

{# 默认菜单项 #}
{% set default_menu_items = [
  { url: '/', title: '首页', active: false },
  { url: '/brands', title: '品牌', active: false },
  { url: '/products', title: '产品', active: false },
  { url: '/designers', title: '设计师', active: false },
  { url: '/news', title: '资讯', active: false },
  { url: '/reviews', title: '评价', active: false }
] %}
{% set menu_items = menu_items|default(default_menu_items) %}

<nav class="bg-white shadow-sm border-b border-gray-200" role="navigation" aria-label="主导航">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">

      {# Logo和品牌区域 #}
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <a href="/" class="flex items-center group" aria-label="回到首页">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center mr-3 transition-transform duration-200 group-hover:scale-105">
              <span class="text-white font-bold text-lg">{{ logo_text|first|upper }}</span>
            </div>
            <div class="flex flex-col">
              <span class="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-200">{{ logo_text }}</span>
              <span class="text-xs text-gray-500 -mt-1">{{ logo_subtitle }}</span>
            </div>
          </a>
        </div>
      </div>

      {# 桌面端主菜单 #}
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-8">
          {% for item in menu_items %}
            <a href="{{ item.url }}" 
               class="px-3 py-2 text-sm font-medium transition-colors duration-200 rounded-md
                      {{ item.active ? 'text-primary-600 bg-primary-50' : 'text-gray-500 hover:text-primary-600 hover:bg-gray-50' }}"
               {% if item.active %}aria-current="page"{% endif %}>
              {{ item.title }}
            </a>
          {% endfor %}
        </div>
      </div>

      {# 右侧功能区域 #}
      <div class="flex items-center space-x-4">
        
        {# 桌面端搜索框 #}
        {% if show_search %}
          <div class="relative hidden md:block">
            <div class="relative">
              <input type="text" 
                     id="search-input" 
                     placeholder="{{ search_placeholder }}"
                     class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg 
                            focus:ring-2 focus:ring-primary-500 focus:border-primary-500 
                            text-sm transition-all duration-200
                            hover:border-gray-400"
                     aria-label="搜索">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </div>
            </div>
            {# 搜索建议容器 #}
            <div id="search-suggestions" 
                 class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 hidden max-h-96 overflow-y-auto">
              {# 搜索建议将由JavaScript填充 #}
            </div>
          </div>
        {% endif %}

        {# 移动端搜索按钮 #}
        {% if show_search %}
          <button class="md:hidden p-2 text-gray-500 hover:text-primary-600 transition-colors duration-200 rounded-md hover:bg-gray-50" 
                  id="mobile-search-btn"
                  aria-label="打开搜索">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
          </button>
        {% endif %}

        {# 语言切换器 #}
        {% if show_language_switcher %}
          <div class="relative">
            <button class="flex items-center text-sm text-gray-500 hover:text-primary-600 transition-colors duration-200 px-2 py-1 rounded-md hover:bg-gray-50"
                    aria-label="切换语言">
              <span class="mr-1">中文</span>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
          </div>
        {% endif %}

        {# 用户菜单 #}
        {% if show_user_menu %}
          <div class="relative">
            <button class="flex items-center text-sm text-gray-500 hover:text-primary-600 transition-colors duration-200 p-2 rounded-md hover:bg-gray-50"
                    aria-label="用户菜单">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
            </button>
          </div>
        {% endif %}

        {# 移动端菜单按钮 #}
        <div class="md:hidden">
          <button type="button" 
                  id="mobile-menu-button" 
                  class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200" 
                  aria-controls="mobile-menu" 
                  aria-expanded="false"
                  aria-label="打开主菜单">
            <span class="sr-only">打开主菜单</span>
            <svg id="menu-open-icon" class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            <svg id="menu-close-icon" class="hidden h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  {# 移动端菜单面板 #}
  <div id="mobile-menu" class="md:hidden hidden" role="menu">
    <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg">
      {% for item in menu_items %}
        <a href="{{ item.url }}" 
           class="block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md
                  {{ item.active ? 'text-primary-600 bg-primary-50' : 'text-gray-500 hover:text-primary-600 hover:bg-gray-50' }}"
           role="menuitem"
           {% if item.active %}aria-current="page"{% endif %}>
          {{ item.title }}
        </a>
      {% endfor %}
    </div>
  </div>
</nav>
