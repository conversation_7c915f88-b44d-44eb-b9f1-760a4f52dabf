{#
/**
 * @file
 * 产品卡片组件 - 基于Tailwind CSS的响应式产品卡片
 * 
 * 可用参数:
 * - product: 产品对象，包含以下属性：
 *   - id: 产品ID
 *   - title: 产品标题
 *   - url: 产品链接
 *   - image: 产品图片
 *   - brand: 品牌名称
 *   - category: 产品分类
 *   - description: 产品描述
 *   - price: 产品价格
 *   - original_price: 原价（可选）
 *   - rating: 评分（1-5）
 * - variant: 卡片变体 ('default', 'compact', 'featured')
 * - show_actions: 是否显示操作按钮 (默认: true)
 * - show_rating: 是否显示评分 (默认: true)
 * - show_description: 是否显示描述 (默认: true)
 */
#}

{# 设置默认值 #}
{% set variant = variant|default('default') %}
{% set show_actions = show_actions|default(true) %}
{% set show_rating = show_rating|default(true) %}
{% set show_description = show_description|default(true) %}

{# 根据变体设置样式类 #}
{% set card_classes = {
  'default': 'bg-white rounded-lg shadow-soft overflow-hidden hover:shadow-medium transition-all duration-300 group',
  'compact': 'bg-white rounded-md shadow-sm overflow-hidden hover:shadow-md transition-all duration-200 group',
  'featured': 'bg-white rounded-xl shadow-medium overflow-hidden hover:shadow-strong transition-all duration-300 group border border-primary-100'
} %}

{% set image_classes = {
  'default': 'aspect-w-1 aspect-h-1',
  'compact': 'aspect-w-4 aspect-h-3',
  'featured': 'aspect-w-1 aspect-h-1'
} %}

<article class="{{ card_classes[variant] }}" 
         data-product-id="{{ product.id }}"
         itemscope 
         itemtype="https://schema.org/Product">
  
  {# 产品图片区域 #}
  {% if product.image %}
    <div class="{{ image_classes[variant] }} bg-gray-100 relative overflow-hidden">
      {% if product.image.url %}
        <img src="{{ product.image.url }}" 
             alt="{{ product.image.alt|default(product.title) }}"
             class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
             loading="lazy"
             itemprop="image">
      {% else %}
        {{ product.image }}
      {% endif %}
      
      {# 悬停操作覆盖层 #}
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
        <div class="flex space-x-2">
          <button class="bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200 transform translate-y-2 group-hover:translate-y-0"
                  aria-label="快速查看 {{ product.title }}"
                  data-action="quick-view"
                  data-product-id="{{ product.id }}">
            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
          </button>
          <button class="bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200 transform translate-y-2 group-hover:translate-y-0"
                  aria-label="收藏 {{ product.title }}"
                  data-action="favorite"
                  data-product-id="{{ product.id }}">
            <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
            </svg>
          </button>
        </div>
      </div>

      {# 特色标签 #}
      {% if variant == 'featured' %}
        <div class="absolute top-3 left-3">
          <span class="bg-primary-600 text-white text-xs font-medium px-2 py-1 rounded-full">
            精选
          </span>
        </div>
      {% endif %}
    </div>
  {% endif %}

  {# 产品信息区域 #}
  <div class="p-4 {{ variant == 'featured' ? 'p-6' : (variant == 'compact' ? 'p-3' : 'p-4') }}">
    
    {# 品牌信息 #}
    {% if product.brand %}
      <div class="text-xs text-gray-500 uppercase tracking-wide mb-1" itemprop="brand">
        {{ product.brand }}
      </div>
    {% endif %}

    {# 产品标题 #}
    <h3 class="{{ variant == 'featured' ? 'text-xl' : 'text-lg' }} font-semibold text-gray-900 mb-2 line-clamp-2" itemprop="name">
      <a href="{{ product.url }}" 
         class="hover:text-primary-600 transition-colors duration-200"
         itemprop="url">
        {{ product.title }}
      </a>
    </h3>

    {# 产品分类 #}
    {% if product.category %}
      <div class="mb-2">
        <span class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full" itemprop="category">
          {{ product.category }}
        </span>
      </div>
    {% endif %}

    {# 产品描述 #}
    {% if show_description and product.description and variant != 'compact' %}
      <div class="text-gray-600 text-sm mb-3 line-clamp-2" itemprop="description">
        {{ product.description }}
      </div>
    {% endif %}

    {# 产品评分 #}
    {% if show_rating and product.rating %}
      <div class="flex items-center mb-3" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
        <div class="flex items-center">
          {% for i in 1..5 %}
            <svg class="w-3 h-3 {{ i <= product.rating ? 'text-yellow-400' : 'text-gray-300' }}" 
                 fill="currentColor" 
                 viewBox="0 0 20 20" 
                 aria-hidden="true">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
            </svg>
          {% endfor %}
        </div>
        <span class="ml-1 text-xs text-gray-500">
          (<span itemprop="ratingValue">{{ product.rating }}</span>)
        </span>
        <meta itemprop="bestRating" content="5">
        <meta itemprop="worstRating" content="1">
      </div>
    {% endif %}

    {# 产品价格 #}
    {% if product.price %}
      <div class="mb-4" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
        <span class="{{ variant == 'featured' ? 'text-xl' : 'text-lg' }} font-bold text-primary-600" itemprop="price">
          ¥{{ product.price }}
        </span>
        {% if product.original_price and product.original_price != product.price %}
          <span class="text-sm text-gray-500 line-through ml-2">
            ¥{{ product.original_price }}
          </span>
        {% endif %}
        <meta itemprop="priceCurrency" content="CNY">
        <meta itemprop="availability" content="https://schema.org/InStock">
      </div>
    {% endif %}

    {# 操作按钮 #}
    {% if show_actions %}
      <div class="flex space-x-2">
        <a href="{{ product.url }}" 
           class="flex-1 bg-primary-600 text-white text-center py-2 px-3 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
          查看详情
        </a>
        {% if variant != 'compact' %}
          <button class="p-2 border border-gray-300 rounded-md hover:border-primary-600 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  aria-label="对比 {{ product.title }}"
                  data-action="compare"
                  data-product-id="{{ product.id }}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </button>
        {% endif %}
      </div>
    {% endif %}
  </div>
</article>
