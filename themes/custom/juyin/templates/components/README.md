# 🧩 Juyin 主题组件库

## 📋 概述

这是基于Tailwind CSS构建的Drupal主题组件库，遵循原子设计方法论，提供可复用、可配置的UI组件。

## 🏗️ 组件架构

```
templates/components/
├── navigation/          # 导航组件
│   └── main-navigation.html.twig
├── cards/              # 卡片组件
│   └── product-card.html.twig
├── buttons/            # 按钮组件
│   └── button.html.twig
├── forms/              # 表单组件
│   └── search-input.html.twig
└── layout/             # 布局组件
    └── (待开发)
```

## 🎯 设计原则

### 1. 组件化优先
- 每个组件都是独立的、可复用的
- 通过参数配置实现不同变体
- 保持组件的单一职责

### 2. Tailwind工具类纯化
- 使用纯Tailwind工具类，避免自定义CSS
- 利用Tailwind的设计系统保持一致性
- 响应式设计内置于组件中

### 3. 可访问性优先
- 遵循WCAG指南
- 提供适当的ARIA标签
- 支持键盘导航

### 4. 性能优化
- 组件按需加载
- 优化的HTML结构
- 最小化JavaScript依赖

## 📚 组件文档

### 🧭 导航组件

#### main-navigation.html.twig

**用途**: 网站主导航栏，包含Logo、菜单、搜索和用户操作

**参数**:
```twig
{% include '@juyin/components/navigation/main-navigation.html.twig' with {
  'logo_text': 'Juyin',                    # 品牌名称
  'logo_subtitle': '意大利家居',            # 品牌副标题
  'menu_items': [                          # 菜单项数组
    { url: '/', title: '首页', active: true },
    { url: '/products', title: '产品', active: false }
  ],
  'search_placeholder': '搜索...',         # 搜索框占位符
  'show_search': true,                     # 是否显示搜索
  'show_user_menu': true,                  # 是否显示用户菜单
  'show_language_switcher': true           # 是否显示语言切换
} %}
```

**特性**:
- 响应式设计，移动端汉堡菜单
- 集成搜索功能
- 支持活动状态标识
- 无障碍访问支持

### 🃏 卡片组件

#### product-card.html.twig

**用途**: 产品展示卡片，支持多种显示变体

**参数**:
```twig
{% include '@juyin/components/cards/product-card.html.twig' with {
  'product': {
    'id': 123,
    'title': '意大利真皮沙发',
    'url': '/product/123',
    'image': image_object,
    'brand': 'Natuzzi',
    'category': '沙发',
    'description': '高品质真皮沙发...',
    'price': '15999',
    'original_price': '19999',
    'rating': 4.5
  },
  'variant': 'default',                    # 'default', 'compact', 'featured'
  'show_actions': true,                    # 是否显示操作按钮
  'show_rating': true,                     # 是否显示评分
  'show_description': true                 # 是否显示描述
} %}
```

**变体**:
- `default`: 标准卡片样式
- `compact`: 紧凑型卡片
- `featured`: 特色卡片（带边框和标签）

### 🔘 按钮组件

#### button.html.twig

**用途**: 通用按钮组件，支持多种样式和状态

**参数**:
```twig
{% include '@juyin/components/buttons/button.html.twig' with {
  'text': '查看详情',                      # 按钮文本
  'url': '/product/123',                   # 链接地址（可选）
  'type': 'button',                        # 'button', 'submit', 'reset'
  'variant': 'primary',                    # 'primary', 'secondary', 'outline', 'ghost', 'danger'
  'size': 'md',                           # 'xs', 'sm', 'md', 'lg', 'xl'
  'icon': 'heart',                        # 图标名称（可选）
  'icon_position': 'left',                # 'left', 'right', 'only'
  'disabled': false,                      # 是否禁用
  'loading': false,                       # 是否显示加载状态
  'full_width': false                     # 是否全宽
} %}
```

**可用图标**: search, heart, share, eye, plus, minus, check, x, arrow-right, arrow-left, download, upload

### 🔍 表单组件

#### search-input.html.twig

**用途**: 高级搜索输入框，支持自动完成和建议

**参数**:
```twig
{% include '@juyin/components/forms/search-input.html.twig' with {
  'placeholder': '搜索产品、品牌、设计师...',
  'value': '',                            # 初始值
  'size': 'md',                          # 'sm', 'md', 'lg'
  'variant': 'default',                  # 'default', 'minimal', 'rounded'
  'show_suggestions': true,              # 是否显示搜索建议
  'show_icon': true,                     # 是否显示搜索图标
  'show_clear': true,                    # 是否显示清除按钮
  'autocomplete': true,                  # 是否启用自动完成
  'debounce': 300,                       # 防抖延迟时间（毫秒）
  'min_chars': 2                         # 最小搜索字符数
} %}
```

**特性**:
- 实时搜索建议
- 防抖输入处理
- 搜索历史记录
- 热门搜索推荐

## 🎨 使用示例

### 基础页面布局

```twig
{# 在页面模板中使用组件 #}
<div class="min-h-screen bg-gray-50">
  {# 导航 #}
  {% include '@juyin/components/navigation/main-navigation.html.twig' with navigation_config %}
  
  {# 主要内容 #}
  <main class="max-w-7xl mx-auto py-6 px-4">
    {# 产品网格 #}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {% for product in products %}
        {% include '@juyin/components/cards/product-card.html.twig' with {
          'product': product,
          'variant': 'default'
        } %}
      {% endfor %}
    </div>
  </main>
</div>
```

### 表单页面

```twig
<div class="max-w-md mx-auto">
  {# 搜索框 #}
  {% include '@juyin/components/forms/search-input.html.twig' with {
    'size': 'lg',
    'variant': 'rounded'
  } %}
  
  {# 提交按钮 #}
  {% include '@juyin/components/buttons/button.html.twig' with {
    'text': '搜索',
    'type': 'submit',
    'variant': 'primary',
    'size': 'lg',
    'icon': 'search',
    'full_width': true
  } %}
</div>
```

## 🔧 开发指南

### 添加新组件

1. 在相应目录下创建组件文件
2. 遵循命名约定：`component-name.html.twig`
3. 添加完整的参数文档
4. 提供使用示例
5. 更新此README文档

### 组件最佳实践

1. **参数验证**: 使用默认值和类型检查
2. **样式一致性**: 使用Tailwind设计系统
3. **可访问性**: 添加适当的ARIA标签
4. **性能优化**: 避免不必要的DOM操作
5. **文档完整**: 提供清晰的使用说明

### 测试组件

1. 在不同视口尺寸下测试响应式效果
2. 验证键盘导航功能
3. 检查屏幕阅读器兼容性
4. 测试不同参数组合的效果

## 📈 性能优化

### CSS优化
- 使用Tailwind的按需生成功能
- 避免自定义CSS类
- 利用Tailwind的优化配置

### JavaScript优化
- 组件JavaScript按需加载
- 使用事件委托减少监听器
- 实现防抖和节流优化

### 图片优化
- 支持懒加载
- 响应式图片
- WebP格式支持

## 🚀 未来计划

### 待开发组件
- [ ] 布局组件（容器、网格）
- [ ] 模态框组件
- [ ] 通知组件
- [ ] 分页组件
- [ ] 面包屑组件
- [ ] 标签组件

### 功能增强
- [ ] 主题切换支持
- [ ] 国际化支持
- [ ] 动画效果增强
- [ ] 更多图标支持

**组件库持续更新中，欢迎贡献和反馈！**
