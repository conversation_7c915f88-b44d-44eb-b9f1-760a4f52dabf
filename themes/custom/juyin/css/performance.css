/**
 * 性能优化和动画效果样式
 */

/* 图片懒加载样式 */
img[data-src] {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  background-color: #f3f4f6;
  background-image: linear-gradient(45deg, #f9fafb 25%, transparent 25%),
                    linear-gradient(-45deg, #f9fafb 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #f9fafb 75%),
                    linear-gradient(-45deg, transparent 75%, #f9fafb 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

img.lazy-loading {
  opacity: 0.3;
  animation: pulse 1.5s ease-in-out infinite;
}

img.lazy-loaded {
  opacity: 1;
  background: none;
}

img.lazy-error {
  opacity: 0.5;
  background: #fee2e2;
  position: relative;
}

img.lazy-error::after {
  content: "图片加载失败";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #dc2626;
  font-size: 12px;
  white-space: nowrap;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

/* 页面过渡效果 */
body {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

body.page-loaded {
  opacity: 1;
}

body.page-leaving {
  opacity: 0;
}

/* 字体加载优化 */
body:not(.fonts-loaded) {
  font-display: swap;
}

.fonts-loaded {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 滚动进度条 */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  z-index: 9999;
  transition: width 0.1s ease-out;
}

/* 回到顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3rem;
  height: 3rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease-in-out;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.back-to-top:hover {
  background: #2563eb;
  transform: translateY(0);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 减少动画（尊重用户偏好） */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .scroll-progress {
    transition: none;
  }
}

/* 图片容器优化 */
.image-container {
  position: relative;
  overflow: hidden;
  background-color: #f3f4f6;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}

.image-container:hover img {
  transform: scale(1.05);
}

/* 骨架屏效果 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 内容淡入动画 */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 卡片悬停效果 */
.card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 按钮加载状态 */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式图片优化 */
.responsive-image {
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* WebP支持检测 */
.webp .webp-image {
  background-image: url('image.webp');
}

.no-webp .webp-image {
  background-image: url('image.jpg');
}

/* 关键路径CSS内联优化 */
.critical-content {
  /* 关键内容样式，应该内联到HTML中 */
  font-family: system-ui, sans-serif;
  line-height: 1.6;
  color: #1f2937;
}

/* 非关键内容延迟加载 */
.non-critical {
  /* 非关键样式，可以异步加载 */
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.non-critical.loaded {
  opacity: 1;
}
