/* @license GPL-2.0-or-later https://www.drupal.org/licensing/faq */
window.drupalTranslations={"strings":{"":{"An AJAX HTTP error occurred.":"\u53d1\u751f\u4e00\u4e2aAJAX HTTP\u9519\u8bef\u3002","HTTP Result Code: !status":"HTTP\u8fd4\u56de\u4ee3\u7801\uff1a!status","StatusText: !statusText":"\u72b6\u6001\u6587\u672c: !statusText","ResponseText: !responseText":"\u54cd\u5e94\u6587\u672c\uff1a !responseText","ReadyState: !readyState":"\u51c6\u5907\u72b6\u6001\uff1a !readyState","CustomMessage: !customMessage":"\u5b9a\u5236\u4fe1\u606f\uff1a !customMessage","Please wait...":"\u8bf7\u7a0d\u7b49...","The callback URL is not local and not trusted: !url":"\u975e\u672c\u5730\u7684\u56de\u8c03\u5730\u5740\u4e0d\u4f1a\u88ab\u4fe1\u4efb\uff1a !url","An error occurred during the execution of the Ajax response: !error":"\u5728\u6267\u884cAjax\u54cd\u5e94\u671f\u95f4\u53d1\u751f\u9519\u8bef: !error","Changed":"\u5df2\u66f4\u6539","List additional actions":"\u5217\u51fa\u989d\u5916\u7684\u52a8\u4f5c","Edit machine name":"\u7f16\u8f91\u673a\u8bfb\u540d\u5b57","Edit":"\u7f16\u8f91","Status message":"\u72b6\u6001\u6d88\u606f","Error message":"\u9519\u8bef\u4fe1\u606f","Warning message":"\u8b66\u544a\u4fe1\u606f","Show all columns":"\u663e\u793a\u6240\u6709\u5217","Hide lower priority columns":"\u9690\u85cf\u8f83\u4f4e\u4f18\u5148\u7ea7\u7684\u5217","Select all rows in this table":"\u9009\u62e9\u6b64\u8868\u4e2d\u7684\u6240\u6709\u884c","Deselect all rows in this table":"\u53d6\u6d88\u5168\u9009\u6b64\u8868\u4e2d\u7684\u6240\u6709\u884c","(active tab)":"\uff08\u6d3b\u52a8\u6807\u7b7e\uff09","Open":"\u5f00\u653e","Close":"\u5173\u95ed","Press the esc key to exit.":"\u6309ESC\u952e\u9000\u51fa\u3002","@action @title configuration options":"@action @title \u914d\u7f6e\u9009\u9879","Requires a title":"\u9700\u8981\u4e00\u4e2a\u6807\u9898","Not published":"\u672a\u53d1\u8868","Don\u0027t display post information":"\u4e0d\u8981\u663e\u793a\u53d1\u5e03\u4fe1\u606f\u3002","Horizontal orientation":"\u6a2a\u5411","Vertical orientation":"\u7eb5\u5411","Extend":"\u6269\u5c55","Collapse":"\u6298\u53e0","@label":"@label","Tray orientation changed to @orientation.":"\u6258\u76d8\u65b9\u5411\u5df2\u66f4\u6539\u4e3a @orientation\u3002","closed":"\u5df2\u5173\u95ed","opened":"\u5df2\u6253\u5f00","Tray \u0022@tray\u0022 @action.":"\u6258\u76d8\u201c@tray\u201d@action\u3002","Tray @action.":"\u6258\u76d8 @action\u3002","!tour_item of !total":"!tour_item \/ !total","End tour":"\u7ed3\u675f\u5bfc\u89c8","Next":"\u4e0b\u4e00\u4e2a","No items selected":"\u6ca1\u6709\u9009\u9879\u88ab\u9009\u4e2d","Close message":"\u5173\u95ed\u6d88\u606f"}},"pluralFormula":{"1":0,"default":1}};;
(function(){const settingsElement=document.querySelector('head > script[type="application/json"][data-drupal-selector="drupal-settings-json"], body > script[type="application/json"][data-drupal-selector="drupal-settings-json"]');window.drupalSettings={};if(settingsElement!==null)window.drupalSettings=JSON.parse(settingsElement.textContent);})();;
window.Drupal={behaviors:{},locale:{}};(function(Drupal,drupalSettings,drupalTranslations,console,Proxy,Reflect){Drupal.throwError=function(error){setTimeout(()=>{throw error;},0);};Drupal.attachBehaviors=function(context,settings){context=context||document;settings=settings||drupalSettings;const behaviors=Drupal.behaviors;Object.keys(behaviors||{}).forEach((i)=>{if(typeof behaviors[i].attach==='function')try{behaviors[i].attach(context,settings);}catch(e){Drupal.throwError(e);}});};Drupal.detachBehaviors=function(context,settings,trigger){context=context||document;settings=settings||drupalSettings;trigger=trigger||'unload';const behaviors=Drupal.behaviors;Object.keys(behaviors||{}).forEach((i)=>{if(typeof behaviors[i].detach==='function')try{behaviors[i].detach(context,settings,trigger);}catch(e){Drupal.throwError(e);}});};Drupal.checkPlain=function(str){str=str.toString().replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;').replace(/"/g,'&quot;').replace(/'/g,'&#39;');return str;};Drupal.formatString=function(str,args){const processedArgs={};Object.keys(args||{}).forEach((key)=>{switch(key.charAt(0)){case '@':processedArgs[key]=Drupal.checkPlain(args[key]);break;case '!':processedArgs[key]=args[key];break;default:processedArgs[key]=Drupal.theme('placeholder',args[key]);break;}});return Drupal.stringReplace(str,processedArgs,null);};Drupal.stringReplace=function(str,args,keys){if(str.length===0)return str;if(!Array.isArray(keys)){keys=Object.keys(args||{});keys.sort((a,b)=>a.length-b.length);}if(keys.length===0)return str;const key=keys.pop();const fragments=str.split(key);if(keys.length){for(let i=0;i<fragments.length;i++)fragments[i]=Drupal.stringReplace(fragments[i],args,keys.slice(0));}return fragments.join(args[key]);};Drupal.t=function(str,args,options){options=options||{};options.context=options.context||'';if(typeof drupalTranslations!=='undefined'&&drupalTranslations.strings&&drupalTranslations.strings[options.context]&&drupalTranslations.strings[options.context][str])str=drupalTranslations.strings[options.context][str];if(args)str=Drupal.formatString(str,args);return str;};Drupal.url=function(path){return drupalSettings.path.baseUrl+drupalSettings.path.pathPrefix+path;};Drupal.url.toAbsolute=function(url){const urlParsingNode=document.createElement('a');try{url=decodeURIComponent(url);}catch(e){}urlParsingNode.setAttribute('href',url);return urlParsingNode.cloneNode(false).href;};Drupal.url.isLocal=function(url){let absoluteUrl=Drupal.url.toAbsolute(url);let {protocol}=window.location;if(protocol==='http:'&&absoluteUrl.startsWith('https:'))protocol='https:';let baseUrl=`${protocol}//${window.location.host}${drupalSettings.path.baseUrl.slice(0,-1)}`;try{absoluteUrl=decodeURIComponent(absoluteUrl);}catch(e){}try{baseUrl=decodeURIComponent(baseUrl);}catch(e){}return absoluteUrl===baseUrl||absoluteUrl.startsWith(`${baseUrl}/`);};Drupal.formatPlural=function(count,singular,plural,args,options){args=args||{};args['@count']=count;const pluralDelimiter=drupalSettings.pluralDelimiter;const translations=Drupal.t(singular+pluralDelimiter+plural,args,options).split(pluralDelimiter);let index=0;if(typeof drupalTranslations!=='undefined'&&drupalTranslations.pluralFormula)index=count in drupalTranslations.pluralFormula?drupalTranslations.pluralFormula[count]:drupalTranslations.pluralFormula.default;else{if(args['@count']!==1)index=1;}return translations[index];};Drupal.encodePath=function(item){return window.encodeURIComponent(item).replace(/%2F/g,'/');};Drupal.deprecationError=({message})=>{if(drupalSettings.suppressDeprecationErrors===false&&typeof console!=='undefined'&&console.warn)console.warn(`[Deprecation] ${message}`);};Drupal.deprecatedProperty=({target,deprecatedProperty,message})=>{if(!Proxy||!Reflect)return target;return new Proxy(target,{get:(target,key,...rest)=>{if(key===deprecatedProperty)Drupal.deprecationError({message});return Reflect.get(target,key,...rest);}});};Drupal.theme=function(func,...args){if(func in Drupal.theme)return Drupal.theme[func](...args);};Drupal.theme.placeholder=function(str){return `<em class="placeholder">${Drupal.checkPlain(str)}</em>`;};Drupal.elementIsVisible=function(elem){return !!(elem.offsetWidth||elem.offsetHeight||elem.getClientRects().length);};Drupal.elementIsHidden=function(elem){return !Drupal.elementIsVisible(elem);};})(Drupal,window.drupalSettings,window.drupalTranslations,window.console,window.Proxy,window.Reflect);;
if(window.jQuery)jQuery.noConflict();document.documentElement.className+=' js';(function(Drupal,drupalSettings){const domReady=(callback)=>{const listener=()=>{callback();document.removeEventListener('DOMContentLoaded',listener);};if(document.readyState!=='loading')setTimeout(callback,0);else document.addEventListener('DOMContentLoaded',listener);};domReady(()=>{Drupal.attachBehaviors(document,drupalSettings);});})(Drupal,window.drupalSettings);;
(function(Drupal,drupalSettings){'use strict';Drupal.behaviors.advancedSearch={attach:function(context,settings){const searchInput=context.querySelector('#search-input');const suggestionsContainer=context.querySelector('#search-suggestions');if(searchInput&&!searchInput.hasAttribute('data-search-enhanced')){this.initAdvancedSearch(searchInput,suggestionsContainer);searchInput.setAttribute('data-search-enhanced','true');}this.initSearchHighlight(context);},initAdvancedSearch:function(searchInput,suggestionsContainer){let searchTimeout;let currentRequest;const searchCache=new Map();searchInput.addEventListener('input',(event)=>{const query=event.target.value.trim();clearTimeout(searchTimeout);if(query.length<2){this.hideSuggestions(suggestionsContainer);return;}searchTimeout=setTimeout(()=>{this.performSearch(query,suggestionsContainer,searchCache);},300);});searchInput.addEventListener('keydown',(event)=>{this.handleKeyboardNavigation(event,suggestionsContainer);});document.addEventListener('click',(event)=>{if(!searchInput.contains(event.target)&&!suggestionsContainer.contains(event.target))this.hideSuggestions(suggestionsContainer);});},performSearch:function(query,suggestionsContainer,searchCache){if(searchCache.has(query)){this.displaySuggestions(searchCache.get(query),suggestionsContainer,query);return;}this.showLoadingState(suggestionsContainer);this.mockSearchRequest(query).then((results)=>{searchCache.set(query,results);this.displaySuggestions(results,suggestionsContainer,query);}).catch((error)=>{console.error('搜索请求失败:',error);this.hideLoadingState(suggestionsContainer);});},mockSearchRequest:function(query){return new Promise((resolve)=>{setTimeout(()=>{const mockResults=[{type:'brand',title:'Cassina',description:'意大利顶级家具品牌',url:'/node/7',image:'/themes/custom/juyin/images/cassina-logo.jpg'},{type:'product',title:'LC2 扶手椅',description:'Le Corbusier设计的经典扶手椅',url:'/node/12',image:'/themes/custom/juyin/images/lc2-chair.jpg'},{type:'designer',title:'Patricia Urquiola',description:'西班牙著名工业设计师',url:'/node/17',image:'/themes/custom/juyin/images/patricia-urquiola.jpg'},{type:'news',title:'2024米兰家具展亮点',description:'探索今年米兰家具展的最新趋势',url:'/node/22',image:'/themes/custom/juyin/images/milan-furniture-fair.jpg'}].filter((item)=>item.title.toLowerCase().includes(query.toLowerCase())||item.description.toLowerCase().includes(query.toLowerCase()));resolve(mockResults);},200);});},displaySuggestions:function(results,container,query){this.hideLoadingState(container);if(results.length===0){container.innerHTML=`
          <div class="p-4 text-center text-gray-500">
            <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
            </svg>
            <p>未找到相关结果</p>
          </div>
        `;container.classList.remove('hidden');return;}const suggestionsHTML=results.map((item,index)=>`
        <div class="suggestion-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0" 
             data-index="${index}" data-url="${item.url}">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                ${this.getTypeIcon(item.type)}
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                ${this.highlightQuery(item.title,query)}
              </p>
              <p class="text-sm text-gray-500 truncate">
                ${this.highlightQuery(item.description,query)}
              </p>
            </div>
            <div class="flex-shrink-0">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${this.getTypeBadgeClass(item.type)}">
                ${this.getTypeLabel(item.type)}
              </span>
            </div>
          </div>
        </div>
      `).join('');container.innerHTML=`
        <div class="suggestions-list">
          ${suggestionsHTML}
        </div>
        <div class="p-3 bg-gray-50 border-t border-gray-200">
          <button class="text-sm text-primary-600 hover:text-primary-700 font-medium">
            查看所有 "${query}" 的搜索结果 →
          </button>
        </div>
      `;container.querySelectorAll('.suggestion-item').forEach((item)=>{item.addEventListener('click',()=>{window.location.href=item.getAttribute('data-url');});});container.classList.remove('hidden');},getTypeIcon:function(type){const icons={brand:'<svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',product:'<svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path></svg>',designer:'<svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20"><path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"></path></svg>',news:'<svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20"><path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path></svg>'};return icons[type]||icons.product;},getTypeLabel:function(type){const labels={brand:'品牌',product:'产品',designer:'设计师',news:'资讯'};return labels[type]||'内容';},getTypeBadgeClass:function(type){const classes={brand:'bg-blue-100 text-blue-800',product:'bg-green-100 text-green-800',designer:'bg-purple-100 text-purple-800',news:'bg-orange-100 text-orange-800'};return classes[type]||'bg-gray-100 text-gray-800';},highlightQuery:function(text,query){if(!query)return text;const regex=new RegExp(`(${query})`,'gi');return text.replace(regex,'<mark class="bg-yellow-200 px-1 rounded">$1</mark>');},showLoadingState:function(container){container.innerHTML=`
        <div class="p-4 text-center">
          <div class="inline-flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm text-gray-600">搜索中...</span>
          </div>
        </div>
      `;container.classList.remove('hidden');},hideLoadingState:function(container){},hideSuggestions:function(container){if(container)container.classList.add('hidden');},handleKeyboardNavigation:function(event,container){const suggestions=container.querySelectorAll('.suggestion-item');if(suggestions.length===0)return;let currentIndex=-1;const currentActive=container.querySelector('.suggestion-item.active');if(currentActive)currentIndex=parseInt(currentActive.getAttribute('data-index'));switch(event.key){case 'ArrowDown':event.preventDefault();currentIndex=Math.min(currentIndex+1,suggestions.length-1);this.setActiveSuggestion(suggestions,currentIndex);break;case 'ArrowUp':event.preventDefault();currentIndex=Math.max(currentIndex-1,-1);this.setActiveSuggestion(suggestions,currentIndex);break;case 'Enter':event.preventDefault();if(currentIndex>=0)suggestions[currentIndex].click();break;case 'Escape':this.hideSuggestions(container);break;}},setActiveSuggestion:function(suggestions,index){suggestions.forEach((item,i)=>{if(i===index)item.classList.add('active','bg-primary-50');else item.classList.remove('active','bg-primary-50');});},initSearchHighlight:function(context){const searchResults=context.querySelectorAll('.search-result');const urlParams=new URLSearchParams(window.location.search);const searchQuery=urlParams.get('keys')||urlParams.get('search');if(searchQuery&&searchResults.length>0)searchResults.forEach((result)=>{this.highlightSearchTerms(result,searchQuery);});},highlightSearchTerms:function(element,query){const walker=document.createTreeWalker(element,NodeFilter.SHOW_TEXT,null,false);const textNodes=[];let node;while(node=walker.nextNode())textNodes.push(node);textNodes.forEach((textNode)=>{const text=textNode.textContent;const regex=new RegExp(`(${query})`,'gi');if(regex.test(text)){const highlightedHTML=text.replace(regex,'<mark class="bg-yellow-200 px-1 rounded font-medium">$1</mark>');const wrapper=document.createElement('span');wrapper.innerHTML=highlightedHTML;textNode.parentNode.replaceChild(wrapper,textNode);}});}};})(Drupal,drupalSettings);;
(function(Drupal){'use strict';Drupal.behaviors.userInteractions={attach:function(context,settings){this.initFavorites(context);this.initSocialShare(context);this.initImageViewer(context);this.initTooltips(context);this.initBackToTop(context);},initFavorites:function(context){const favoriteButtons=context.querySelectorAll('.favorite-btn:not(.processed)');favoriteButtons.forEach((button)=>{button.classList.add('processed');button.addEventListener('click',this.handleFavoriteClick.bind(this));});},handleFavoriteClick:function(event){event.preventDefault();const button=event.currentTarget;const nodeId=button.getAttribute('data-node-id');const isFavorited=button.classList.contains('favorited');button.classList.add('loading');this.toggleFavorite(nodeId,!isFavorited).then((result)=>{if(result.success)if(isFavorited){button.classList.remove('favorited');button.querySelector('.favorite-text').textContent='收藏';this.showNotification('已取消收藏','info');}else{button.classList.add('favorited');button.querySelector('.favorite-text').textContent='已收藏';this.showNotification('已添加到收藏','success');}}).catch((error)=>{console.error('收藏操作失败:',error);this.showNotification('操作失败，请重试','error');}).finally(()=>{button.classList.remove('loading');});},toggleFavorite:function(nodeId,favorite){return new Promise((resolve)=>{setTimeout(()=>{const favorites=JSON.parse(localStorage.getItem('favorites')||'[]');if(favorite){if(!favorites.includes(nodeId))favorites.push(nodeId);}else{const index=favorites.indexOf(nodeId);if(index>-1)favorites.splice(index,1);}localStorage.setItem('favorites',JSON.stringify(favorites));resolve({success:true});},500);});},initSocialShare:function(context){const shareButtons=context.querySelectorAll('.share-btn:not(.processed)');shareButtons.forEach((button)=>{button.classList.add('processed');button.addEventListener('click',this.handleShareClick.bind(this));});const sharePanels=context.querySelectorAll('.share-panel:not(.processed)');sharePanels.forEach((panel)=>{panel.classList.add('processed');this.initSharePanel(panel);});},handleShareClick:function(event){event.preventDefault();const button=event.currentTarget;const sharePanel=button.nextElementSibling;if(sharePanel&&sharePanel.classList.contains('share-panel'))sharePanel.classList.toggle('hidden');},initSharePanel:function(panel){const currentUrl=encodeURIComponent(window.location.href);const currentTitle=encodeURIComponent(document.title);const shareLinks={weibo:`https://service.weibo.com/share/share.php?url=${currentUrl}&title=${currentTitle}`,wechat:'#',qq:`https://connect.qq.com/widget/shareqq/index.html?url=${currentUrl}&title=${currentTitle}`,email:`mailto:?subject=${currentTitle}&body=${currentUrl}`};Object.keys(shareLinks).forEach((platform)=>{const link=panel.querySelector(`[data-share="${platform}"]`);if(link&&platform!=='wechat'){link.href=shareLinks[platform];link.target='_blank';}});const copyButton=panel.querySelector('.copy-link-btn');if(copyButton)copyButton.addEventListener('click',()=>{navigator.clipboard.writeText(window.location.href).then(()=>{this.showNotification('链接已复制到剪贴板','success');}).catch(()=>{this.showNotification('复制失败，请手动复制','error');});});},initImageViewer:function(context){const images=context.querySelectorAll('.zoomable-image:not(.processed)');images.forEach((img)=>{img.classList.add('processed');img.addEventListener('click',this.openImageViewer.bind(this));});},openImageViewer:function(event){const img=event.currentTarget;const src=img.src||img.getAttribute('data-src');const alt=img.alt||'图片';const modal=document.createElement('div');modal.className='image-viewer-modal fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50';modal.innerHTML=`
        <div class="relative max-w-full max-h-full p-4">
          <img src="${src}" alt="${alt}" class="max-w-full max-h-full object-contain">
          <button class="absolute top-4 right-4 text-white hover:text-gray-300 text-2xl font-bold">
            ×
          </button>
        </div>
      `;modal.addEventListener('click',(e)=>{if(e.target===modal||e.target.tagName==='BUTTON'){document.body.removeChild(modal);document.body.classList.remove('overflow-hidden');}});const handleEsc=(e)=>{if(e.key==='Escape'){document.body.removeChild(modal);document.body.classList.remove('overflow-hidden');document.removeEventListener('keydown',handleEsc);}};document.addEventListener('keydown',handleEsc);document.body.appendChild(modal);document.body.classList.add('overflow-hidden');},initTooltips:function(context){const tooltipElements=context.querySelectorAll('[data-tooltip]:not(.processed)');tooltipElements.forEach((element)=>{element.classList.add('processed');let tooltip;element.addEventListener('mouseenter',()=>{const text=element.getAttribute('data-tooltip');tooltip=this.createTooltip(text);document.body.appendChild(tooltip);this.positionTooltip(tooltip,element);});element.addEventListener('mouseleave',()=>{if(tooltip){document.body.removeChild(tooltip);tooltip=null;}});});},createTooltip:function(text){const tooltip=document.createElement('div');tooltip.className='tooltip absolute bg-gray-900 text-white text-sm px-2 py-1 rounded shadow-lg z-50 pointer-events-none';tooltip.textContent=text;return tooltip;},positionTooltip:function(tooltip,element){const rect=element.getBoundingClientRect();const tooltipRect=tooltip.getBoundingClientRect();let top=rect.top-tooltipRect.height-8;let left=rect.left+(rect.width-tooltipRect.width)/2;if(top<0)top=rect.bottom+8;if(left<0)left=8;if(left+tooltipRect.width>window.innerWidth)left=window.innerWidth-tooltipRect.width-8;tooltip.style.top=top+window.scrollY+'px';tooltip.style.left=left+'px';},initBackToTop:function(context){if(context!==document)return;const backToTop=document.createElement('button');backToTop.className='back-to-top fixed bottom-8 right-8 w-12 h-12 bg-primary-600 text-white rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 opacity-0 invisible';backToTop.innerHTML=`
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
      `;backToTop.addEventListener('click',()=>{window.scrollTo({top:0,behavior:'smooth'});});document.body.appendChild(backToTop);let ticking=false;const handleScroll=()=>{if(!ticking){requestAnimationFrame(()=>{if(window.scrollY>300)backToTop.classList.remove('opacity-0','invisible');else backToTop.classList.add('opacity-0','invisible');ticking=false;});ticking=true;}};window.addEventListener('scroll',handleScroll,{passive:true});},showNotification:function(message,type='info'){const notification=document.createElement('div');notification.className=`notification fixed top-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300 ${this.getNotificationClass(type)}`;notification.innerHTML=`
        <div class="flex items-center">
          ${this.getNotificationIcon(type)}
          <span class="ml-2">${message}</span>
          <button class="ml-4 text-current opacity-70 hover:opacity-100">×</button>
        </div>
      `;notification.querySelector('button').addEventListener('click',()=>{this.hideNotification(notification);});document.body.appendChild(notification);setTimeout(()=>{notification.classList.remove('translate-x-full');},100);setTimeout(()=>{this.hideNotification(notification);},5000);},hideNotification:function(notification){notification.classList.add('translate-x-full');setTimeout(()=>{if(notification.parentNode)document.body.removeChild(notification);},300);},getNotificationClass:function(type){const classes={success:'bg-green-500 text-white',error:'bg-red-500 text-white',warning:'bg-yellow-500 text-white',info:'bg-blue-500 text-white'};return classes[type]||classes.info;},getNotificationIcon:function(type){const icons={success:'<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',error:'<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',warning:'<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',info:'<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'};return icons[type]||icons.info;}};})(Drupal);;
(function(Drupal){'use strict';Drupal.behaviors.lazyLoading={attach:function(context,settings){if('IntersectionObserver' in window)this.initIntersectionObserver(context);else this.loadAllImages(context);this.initPerformanceOptimizations(context);},initIntersectionObserver:function(context){const images=context.querySelectorAll('img[data-src]:not(.lazy-loaded)');if(images.length===0)return;const imageObserver=new IntersectionObserver((entries,observer)=>{entries.forEach((entry)=>{if(entry.isIntersecting){const img=entry.target;this.loadImage(img);observer.unobserve(img);}});},{rootMargin:'50px 0px',threshold:0.01});images.forEach((img)=>{imageObserver.observe(img);});},loadImage:function(img){const src=img.getAttribute('data-src');const srcset=img.getAttribute('data-srcset');if(!src)return;const imageLoader=new Image();imageLoader.onload=()=>{img.src=src;if(srcset)img.srcset=srcset;img.classList.add('lazy-loaded');img.classList.remove('lazy-loading');img.removeAttribute('data-src');img.removeAttribute('data-srcset');};imageLoader.onerror=()=>{img.classList.add('lazy-error');img.classList.remove('lazy-loading');};img.classList.add('lazy-loading');imageLoader.src=src;if(srcset)imageLoader.srcset=srcset;},loadAllImages:function(context){const images=context.querySelectorAll('img[data-src]');images.forEach((img)=>{this.loadImage(img);});},initPerformanceOptimizations:function(context){this.preloadCriticalResources();this.optimizeFontLoading();this.initScrollOptimization(context);},preloadCriticalResources:function(){const criticalCSS=document.querySelector('link[rel="stylesheet"][href*="tailwind"]');if(criticalCSS&&!criticalCSS.hasAttribute('data-preloaded')){const preloadLink=document.createElement('link');preloadLink.rel='preload';preloadLink.as='style';preloadLink.href=criticalCSS.href;document.head.appendChild(preloadLink);criticalCSS.setAttribute('data-preloaded','true');}const heroImages=document.querySelectorAll('.hero img, .featured img');heroImages.forEach((img)=>{if(img.getAttribute('data-src'))this.loadImage(img);});},optimizeFontLoading:function(){if('fonts' in document)document.fonts.ready.then(()=>{document.body.classList.add('fonts-loaded');});},initScrollOptimization:function(context){let ticking=false;const optimizedScroll=()=>{this.updateScrollIndicators();ticking=false;};const onScroll=()=>{if(!ticking){requestAnimationFrame(optimizedScroll);ticking=true;}};window.addEventListener('scroll',onScroll,{passive:true});},updateScrollIndicators:function(){const scrolled=window.pageYOffset;const rate=scrolled/(document.body.scrollHeight-window.innerHeight);const progressBar=document.querySelector('.scroll-progress');if(progressBar)progressBar.style.width=(rate*100)+'%';const backToTop=document.querySelector('.back-to-top');if(backToTop)if(scrolled>300)backToTop.classList.add('visible');else backToTop.classList.remove('visible');}};Drupal.behaviors.pageTransitions={attach:function(context,settings){if(context===document)document.body.classList.add('page-loaded');const internalLinks=context.querySelectorAll('a[href^="/"], a[href^="'+window.location.origin+'"]');internalLinks.forEach((link)=>{if(!link.hasAttribute('data-transition-added')){link.addEventListener('click',this.handleLinkClick.bind(this));link.setAttribute('data-transition-added','true');}});},handleLinkClick:function(event){const link=event.currentTarget;const href=link.getAttribute('href');if(link.hasAttribute('download')||link.getAttribute('target')==='_blank'||href.includes('#')||event.ctrlKey||event.metaKey)return;document.body.classList.add('page-leaving');setTimeout(()=>{window.location.href=href;},150);}};})(Drupal);;
