<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/layout/page--front.html.twig */
class __TwigTemplate_61aeae8dd489baa6a4a415c494e594d2 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 8
        yield "<div class=\"min-h-screen bg-gray-50\">
  
  ";
        // line 11
        yield "  <nav class=\"bg-white shadow-sm border-b border-gray-200\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">
      <div class=\"flex justify-between items-center h-16\">
        
        ";
        // line 16
        yield "        <div class=\"flex items-center\">
          <div class=\"flex-shrink-0\">
            <a href=\"/\" class=\"flex items-center\">
              <div class=\"w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center mr-3\">
                <span class=\"text-white font-bold text-lg\">J</span>
              </div>
              <span class=\"text-xl font-bold text-gray-900\">Juyin</span>
              <span class=\"text-sm text-gray-500 ml-2\">意大利家居</span>
            </a>
          </div>
        </div>

        ";
        // line 29
        yield "        <div class=\"hidden md:block\">
          <div class=\"ml-10 flex items-baseline space-x-8\">
            <a href=\"/\" class=\"text-primary-600 hover:text-primary-700 px-3 py-2 text-sm font-medium transition-colors duration-200\">首页</a>
            <a href=\"/brands\" class=\"text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\">品牌</a>
            <a href=\"/products\" class=\"text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\">产品</a>
            <a href=\"/designers\" class=\"text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\">设计师</a>
            <a href=\"/news\" class=\"text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\">资讯</a>
            <a href=\"/reviews\" class=\"text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors duration-200\">评价</a>
          </div>
        </div>

        ";
        // line 41
        yield "        <div class=\"flex items-center space-x-4\">
          <button class=\"p-2 text-gray-500 hover:text-primary-600 transition-colors duration-200\">
            <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"/>
            </svg>
          </button>
          <div class=\"relative\">
            <button class=\"flex items-center text-sm text-gray-500 hover:text-primary-600 transition-colors duration-200\">
              <span class=\"mr-1\">中文</span>
              <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/>
              </svg>
            </button>
          </div>
          <div class=\"relative\">
            <button class=\"flex items-center text-sm text-gray-500 hover:text-primary-600 transition-colors duration-200\">
              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </nav>

  ";
        // line 68
        yield "  <section class=\"relative bg-gradient-to-br from-primary-50 to-secondary-50 overflow-hidden\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">
      <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">
        
        ";
        // line 73
        yield "        <div class=\"text-center lg:text-left\">
          <h1 class=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6\">
            <span class=\"block\">意大利</span>
            <span class=\"block text-primary-600\">家居艺术</span>
          </h1>
          <p class=\"text-xl text-gray-600 mb-8 max-w-2xl\">
            探索来自意大利的顶级家具品牌、设计师作品和家居灵感。让您的家成为艺术品。
          </p>
          <div class=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">
            <a href=\"/products\" class=\"bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-200\">
              探索产品
            </a>
            <a href=\"/designers\" class=\"border border-primary-600 text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200\">
              认识设计师
            </a>
          </div>
        </div>

        ";
        // line 92
        yield "        <div class=\"relative\">
          <div class=\"aspect-w-4 aspect-h-3 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-2xl overflow-hidden shadow-2xl\">
            <img src=\"/themes/custom/juyin/images/hero-furniture.jpg\" alt=\"意大利家具\" class=\"object-cover w-full h-full\">
          </div>
          
          ";
        // line 98
        yield "          <div class=\"absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-4 hidden lg:block\">
            <div class=\"flex items-center space-x-3\">
              <div class=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\">
                <svg class=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z\"/>
                </svg>
              </div>
              <div>
                <div class=\"font-semibold text-gray-900\">100+</div>
                <div class=\"text-sm text-gray-500\">设计师</div>
              </div>
            </div>
          </div>

          <div class=\"absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-4 hidden lg:block\">
            <div class=\"flex items-center space-x-3\">
              <div class=\"w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center\">
                <svg class=\"w-6 h-6 text-secondary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"/>
                </svg>
              </div>
              <div>
                <div class=\"font-semibold text-gray-900\">5000+</div>
                <div class=\"text-sm text-gray-500\">满意客户</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    ";
        // line 130
        yield "    <div class=\"absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-gradient-to-br from-primary-200 to-secondary-200 rounded-full opacity-20\"></div>
    <div class=\"absolute bottom-0 left-0 -mb-20 -ml-20 w-60 h-60 bg-gradient-to-br from-accent-200 to-primary-200 rounded-full opacity-20\"></div>
  </section>

  ";
        // line 135
        yield "  <section class=\"py-16 bg-white\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">
      <div class=\"text-center mb-12\">
        <h2 class=\"text-3xl font-bold text-gray-900 mb-4\">精选意大利品牌</h2>
        <p class=\"text-lg text-gray-600 max-w-2xl mx-auto\">
          与世界顶级意大利家具品牌合作，为您带来最纯正的意式设计体验
        </p>
      </div>
      
      ";
        // line 145
        yield "      <div class=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8\">
        ";
        // line 146
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(range(1, 5));
        foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
            // line 147
            yield "          <div class=\"flex items-center justify-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer\">
            <div class=\"text-center\">
              <div class=\"w-16 h-16 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg mx-auto mb-3 flex items-center justify-center\">
                <span class=\"text-primary-600 font-bold text-lg\">B";
            // line 150
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $context["i"], "html", null, true);
            yield "</span>
              </div>
              <div class=\"text-sm font-medium text-gray-700\">品牌 ";
            // line 152
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $context["i"], "html", null, true);
            yield "</div>
            </div>
          </div>
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 156
        yield "      </div>
    </div>
  </section>

  ";
        // line 161
        yield "  <main class=\"py-16\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">
      ";
        // line 163
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "content", [], "any", false, false, true, 163), "html", null, true);
        yield "
    </div>
  </main>

  ";
        // line 168
        yield "  <section class=\"bg-primary-600 py-16\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">
      <h2 class=\"text-3xl font-bold text-white mb-4\">订阅我们的资讯</h2>
      <p class=\"text-primary-100 text-lg mb-8 max-w-2xl mx-auto\">
        获取最新的意大利家居设计趋势、产品发布和独家优惠信息
      </p>
      <div class=\"max-w-md mx-auto flex\">
        <input type=\"email\" placeholder=\"输入您的邮箱地址\" class=\"flex-1 px-4 py-3 rounded-l-lg border-0 focus:ring-2 focus:ring-primary-300\">
        <button class=\"bg-secondary-600 text-white px-6 py-3 rounded-r-lg hover:bg-secondary-700 transition-colors duration-200 font-semibold\">
          订阅
        </button>
      </div>
    </div>
  </section>

  ";
        // line 184
        yield "  <footer class=\"bg-gray-900 text-white\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">
      
      ";
        // line 188
        yield "      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">
        
        ";
        // line 191
        yield "        <div class=\"col-span-1 lg:col-span-2\">
          <div class=\"flex items-center mb-4\">
            <div class=\"w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center mr-3\">
              <span class=\"text-white font-bold text-lg\">J</span>
            </div>
            <span class=\"text-xl font-bold\">Juyin</span>
            <span class=\"text-sm text-gray-400 ml-2\">意大利家居</span>
          </div>
          <p class=\"text-gray-400 mb-4 max-w-md\">
            专注于意大利高端家居产品，为您提供最优质的家具、设计师作品和家居资讯。
          </p>
          <div class=\"flex space-x-4\">
            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">
              <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">
                <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>
              </svg>
            </a>
            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">
              <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">
                <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>
              </svg>
            </a>
            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">
              <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">
                <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z\"/>
              </svg>
            </a>
          </div>
        </div>

        ";
        // line 222
        yield "        <div>
          <h3 class=\"text-lg font-semibold mb-4\">快速链接</h3>
          <ul class=\"space-y-2\">
            <li><a href=\"/about\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">关于我们</a></li>
            <li><a href=\"/contact\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">联系我们</a></li>
            <li><a href=\"/careers\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">招聘信息</a></li>
            <li><a href=\"/press\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">媒体中心</a></li>
          </ul>
        </div>

        ";
        // line 233
        yield "        <div>
          <h3 class=\"text-lg font-semibold mb-4\">客户服务</h3>
          <ul class=\"space-y-2\">
            <li><a href=\"/help\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">帮助中心</a></li>
            <li><a href=\"/shipping\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">配送信息</a></li>
            <li><a href=\"/returns\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">退换货政策</a></li>
            <li><a href=\"/privacy\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">隐私政策</a></li>
          </ul>
        </div>
      </div>

      ";
        // line 245
        yield "      <div class=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">
        <p class=\"text-gray-400 text-sm\">
          © 2024 Juyin 意大利家居. 保留所有权利.
        </p>
        <div class=\"flex items-center space-x-4 mt-4 md:mt-0\">
          <span class=\"text-gray-400 text-sm\">Powered by</span>
          <span class=\"text-primary-400 font-semibold\">Drupal 10</span>
        </div>
      </div>

      ";
        // line 256
        yield "      ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "footer", [], "any", false, false, true, 256)) {
            // line 257
            yield "        <div class=\"mt-8\">
          ";
            // line 258
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "footer", [], "any", false, false, true, 258), "html", null, true);
            yield "
        </div>
      ";
        }
        // line 261
        yield "    </div>
  </footer>

</div>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["page"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/layout/page--front.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  343 => 261,  337 => 258,  334 => 257,  331 => 256,  319 => 245,  306 => 233,  294 => 222,  262 => 191,  258 => 188,  253 => 184,  236 => 168,  229 => 163,  225 => 161,  219 => 156,  209 => 152,  204 => 150,  199 => 147,  195 => 146,  192 => 145,  181 => 135,  175 => 130,  142 => 98,  135 => 92,  115 => 73,  109 => 68,  81 => 41,  68 => 29,  54 => 16,  48 => 11,  44 => 8,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/layout/page--front.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/layout/page--front.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["for" => 146, "if" => 256];
        static $filters = ["escape" => 150];
        static $functions = ["range" => 146];

        try {
            $this->sandbox->checkSecurity(
                ['for', 'if'],
                ['escape'],
                ['range'],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
