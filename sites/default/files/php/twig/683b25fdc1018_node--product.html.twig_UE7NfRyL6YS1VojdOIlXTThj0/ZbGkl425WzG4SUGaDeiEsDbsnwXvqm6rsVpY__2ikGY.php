<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/content/node--product.html.twig */
class __TwigTemplate_b45fee98e478242ed7502faf58e6b4f1 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 7
        yield "
";
        // line 9
        $context["product_data"] = ["id" => CoreExtension::getAttribute($this->env, $this->source,         // line 10
($context["node"] ?? null), "id", [], "method", false, false, true, 10), "title" =>         // line 11
($context["label"] ?? null), "url" =>         // line 12
($context["url"] ?? null), "image" => CoreExtension::getAttribute($this->env, $this->source,         // line 13
($context["content"] ?? null), "field_product_images", [], "any", false, false, true, 13), "brand" => ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,         // line 14
($context["content"] ?? null), "field_product_brand", [], "any", false, true, true, 14), "#items", [], "array", false, true, true, 14), 0, [], "any", false, true, true, 14), "value", [], "any", true, true, true, 14)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v0 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_brand", [], "any", false, false, true, 14)) && is_array($_v0) || $_v0 instanceof ArrayAccess && in_array($_v0::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v0["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_brand", [], "any", false, false, true, 14), "#items", [], "array", false, false, true, 14)), 0, [], "any", false, false, true, 14), "value", [], "any", false, false, true, 14), "")) : ("")), "category" => ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,         // line 15
($context["content"] ?? null), "field_product_category", [], "any", false, true, true, 15), "#items", [], "array", false, true, true, 15), 0, [], "any", false, true, true, 15), "value", [], "any", true, true, true, 15)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v1 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_category", [], "any", false, false, true, 15)) && is_array($_v1) || $_v1 instanceof ArrayAccess && in_array($_v1::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v1["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_category", [], "any", false, false, true, 15), "#items", [], "array", false, false, true, 15)), 0, [], "any", false, false, true, 15), "value", [], "any", false, false, true, 15), "")) : ("")), "description" => ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,         // line 16
($context["content"] ?? null), "field_product_description", [], "any", false, true, true, 16), "#items", [], "array", false, true, true, 16), 0, [], "any", false, true, true, 16), "value", [], "any", true, true, true, 16)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v2 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_description", [], "any", false, false, true, 16)) && is_array($_v2) || $_v2 instanceof ArrayAccess && in_array($_v2::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v2["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_description", [], "any", false, false, true, 16), "#items", [], "array", false, false, true, 16)), 0, [], "any", false, false, true, 16), "value", [], "any", false, false, true, 16), "")) : ("")), "price" => ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,         // line 17
($context["content"] ?? null), "field_product_price", [], "any", false, true, true, 17), "#items", [], "array", false, true, true, 17), 0, [], "any", false, true, true, 17), "value", [], "any", true, true, true, 17)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v3 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_price", [], "any", false, false, true, 17)) && is_array($_v3) || $_v3 instanceof ArrayAccess && in_array($_v3::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v3["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_price", [], "any", false, false, true, 17), "#items", [], "array", false, false, true, 17)), 0, [], "any", false, false, true, 17), "value", [], "any", false, false, true, 17), "")) : ("")), "original_price" => ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,         // line 18
($context["content"] ?? null), "field_original_price", [], "any", false, true, true, 18), "#items", [], "array", false, true, true, 18), 0, [], "any", false, true, true, 18), "value", [], "any", true, true, true, 18)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v4 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_original_price", [], "any", false, false, true, 18)) && is_array($_v4) || $_v4 instanceof ArrayAccess && in_array($_v4::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v4["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_original_price", [], "any", false, false, true, 18), "#items", [], "array", false, false, true, 18)), 0, [], "any", false, false, true, 18), "value", [], "any", false, false, true, 18), "")) : ("")), "rating" => ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,         // line 19
($context["content"] ?? null), "field_product_rating", [], "any", false, true, true, 19), "#items", [], "array", false, true, true, 19), 0, [], "any", false, true, true, 19), "value", [], "any", true, true, true, 19)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v5 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_rating", [], "any", false, false, true, 19)) && is_array($_v5) || $_v5 instanceof ArrayAccess && in_array($_v5::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v5["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_product_rating", [], "any", false, false, true, 19), "#items", [], "array", false, false, true, 19)), 0, [], "any", false, false, true, 19), "value", [], "any", false, false, true, 19), 0)) : (0))];
        // line 21
        yield "
";
        // line 23
        $context["card_variant"] = (((($context["view_mode"] ?? null) == "teaser")) ? ("compact") : (((CoreExtension::getAttribute($this->env, $this->source, ($context["node"] ?? null), "isPromoted", [], "method", false, false, true, 23)) ? ("featured") : ("default"))));
        // line 24
        yield "
";
        // line 26
        yield from $this->loadTemplate("@juyin/components/cards/product-card.html.twig", "themes/custom/juyin/templates/content/node--product.html.twig", 26)->unwrap()->yield(CoreExtension::merge($context, ["product" =>         // line 27
($context["product_data"] ?? null), "variant" =>         // line 28
($context["card_variant"] ?? null), "show_actions" => true, "show_rating" => true, "show_description" => (        // line 31
($context["view_mode"] ?? null) != "teaser")]));
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["node", "label", "url", "content", "view_mode"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/content/node--product.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  70 => 31,  69 => 28,  68 => 27,  67 => 26,  64 => 24,  62 => 23,  59 => 21,  57 => 19,  56 => 18,  55 => 17,  54 => 16,  53 => 15,  52 => 14,  51 => 13,  50 => 12,  49 => 11,  48 => 10,  47 => 9,  44 => 7,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/content/node--product.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/content/node--product.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["set" => 9, "include" => 26];
        static $filters = ["default" => 14];
        static $functions = [];

        try {
            $this->sandbox->checkSecurity(
                ['set', 'include'],
                ['default'],
                [],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
