<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/content/node--review.html.twig */
class __TwigTemplate_5c2ddb2abea4cb1f4874d11f28a6dc3e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 7
        yield "
<article";
        // line 8
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", ["review-card", "bg-white", "rounded-lg", "shadow-soft", "overflow-hidden", "hover:shadow-medium", "transition-shadow", "duration-300", "border-l-4", "border-primary-500"], "method", false, false, true, 8), "html", null, true);
        yield ">
  
  <div class=\"p-6\">
    ";
        // line 12
        yield "    <div class=\"review-header flex items-start justify-between mb-4\">
      <div class=\"flex items-center space-x-3\">
        ";
        // line 15
        yield "        <div class=\"reviewer-avatar\">
          ";
        // line 16
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_reviewer_avatar", [], "any", false, false, true, 16)) {
            // line 17
            yield "            ";
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_reviewer_avatar", [], "any", false, false, true, 17), "html", null, true);
            yield "
          ";
        } else {
            // line 19
            yield "            <div class=\"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full flex items-center justify-center\">
              <span class=\"text-white font-bold text-lg\">
                ";
            // line 21
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, Twig\Extension\CoreExtension::upper($this->env->getCharset(), Twig\Extension\CoreExtension::first($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v0 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_author_name", [], "any", false, false, true, 21)) && is_array($_v0) || $_v0 instanceof ArrayAccess && in_array($_v0::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v0["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_author_name", [], "any", false, false, true, 21), "#items", [], "array", false, false, true, 21)), 0, [], "any", false, false, true, 21), "value", [], "any", false, false, true, 21))), "html", null, true);
            yield "
              </span>
            </div>
          ";
        }
        // line 25
        yield "        </div>

        <div class=\"reviewer-info\">
          ";
        // line 29
        yield "          <h4 class=\"font-semibold text-gray-900\">
            ";
        // line 30
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_author_name", [], "any", false, false, true, 30), "html", null, true);
        yield "
          </h4>
          
          ";
        // line 34
        yield "          <div class=\"flex items-center text-sm text-gray-500\">
            <svg class=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"/>
            </svg>
            ";
        // line 38
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["node"] ?? null), "created", [], "any", false, false, true, 38), "value", [], "any", false, false, true, 38), "Y年m月d日"), "html", null, true);
        yield "
          </div>
        </div>
      </div>

      ";
        // line 44
        yield "      ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_rating", [], "any", false, false, true, 44)) {
            // line 45
            yield "        <div class=\"review-rating flex items-center\">
          <div class=\"flex items-center mr-2\">
            ";
            // line 47
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, 5));
            foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
                // line 48
                yield "              <svg class=\"w-5 h-5 ";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(((($context["i"] <= CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v1 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_rating", [], "any", false, false, true, 48)) && is_array($_v1) || $_v1 instanceof ArrayAccess && in_array($_v1::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v1["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_rating", [], "any", false, false, true, 48), "#items", [], "array", false, false, true, 48)), 0, [], "any", false, false, true, 48), "value", [], "any", false, false, true, 48))) ? ("text-yellow-400") : ("text-gray-300")));
                yield "\" fill=\"currentColor\" viewBox=\"0 0 20 20\">
                <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"/>
              </svg>
            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 52
            yield "          </div>
          <span class=\"text-lg font-bold text-gray-900\">
            ";
            // line 54
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v2 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_rating", [], "any", false, false, true, 54)) && is_array($_v2) || $_v2 instanceof ArrayAccess && in_array($_v2::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v2["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_rating", [], "any", false, false, true, 54), "#items", [], "array", false, false, true, 54)), 0, [], "any", false, false, true, 54), "value", [], "any", false, false, true, 54), "html", null, true);
            yield "
          </span>
        </div>
      ";
        }
        // line 58
        yield "    </div>

    ";
        // line 61
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_type", [], "any", false, false, true, 61)) {
            // line 62
            yield "      <div class=\"review-type mb-3\">
        <span class=\"inline-block bg-accent-100 text-accent-800 text-xs px-3 py-1 rounded-full font-medium\">
          ";
            // line 64
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_type", [], "any", false, false, true, 64), "html", null, true);
            yield "
        </span>
      </div>
    ";
        }
        // line 68
        yield "
    ";
        // line 70
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_content", [], "any", false, false, true, 70)) {
            // line 71
            yield "      <div class=\"review-content text-gray-700 mb-4 leading-relaxed\">
        ";
            // line 72
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_content", [], "any", false, false, true, 72), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 75
        yield "
    ";
        // line 77
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_images", [], "any", false, false, true, 77)) {
            // line 78
            yield "      <div class=\"review-images mb-4\">
        <div class=\"grid grid-cols-2 md:grid-cols-3 gap-2\">
          ";
            // line 80
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (($_v3 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_images", [], "any", false, false, true, 80)) && is_array($_v3) || $_v3 instanceof ArrayAccess && in_array($_v3::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v3["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_images", [], "any", false, false, true, 80), "#items", [], "array", false, false, true, 80)), 0, 6));
            foreach ($context['_seq'] as $context["_key"] => $context["image"]) {
                // line 81
                yield "            <div class=\"aspect-w-1 aspect-h-1 bg-gray-100 rounded-md overflow-hidden cursor-pointer hover:opacity-90 transition-opacity duration-200\">
              <img src=\"";
                // line 82
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["image"], "entity", [], "any", false, false, true, 82), "uri", [], "any", false, false, true, 82), "value", [], "any", false, false, true, 82), "html", null, true);
                yield "\" alt=\"Review Image\" class=\"object-cover w-full h-full\">
            </div>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['image'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 85
            yield "        </div>
      </div>
    ";
        }
        // line 88
        yield "
    ";
        // line 90
        yield "    <div class=\"reviewed-item bg-gray-50 rounded-lg p-4 mb-4\">
      <h5 class=\"text-sm font-medium text-gray-700 mb-2\">评价对象</h5>
      
      <div class=\"flex items-center space-x-3\">
        ";
        // line 95
        yield "        ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 95)) {
            // line 96
            yield "          <div class=\"w-12 h-12 bg-white rounded-md overflow-hidden border\">
            ";
            // line 97
            if (CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v4 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 97)) && is_array($_v4) || $_v4 instanceof ArrayAccess && in_array($_v4::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v4["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 97), "#items", [], "array", false, false, true, 97)), 0, [], "any", false, false, true, 97), "entity", [], "any", false, false, true, 97), "field_brand_logo", [], "any", false, false, true, 97)) {
                // line 98
                yield "              <img src=\"";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v5 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 98)) && is_array($_v5) || $_v5 instanceof ArrayAccess && in_array($_v5::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v5["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 98), "#items", [], "array", false, false, true, 98)), 0, [], "any", false, false, true, 98), "entity", [], "any", false, false, true, 98), "field_brand_logo", [], "any", false, false, true, 98), "entity", [], "any", false, false, true, 98), "uri", [], "any", false, false, true, 98), "value", [], "any", false, false, true, 98), "html", null, true);
                yield "\" alt=\"Brand Logo\" class=\"w-full h-full object-cover\">
            ";
            } else {
                // line 100
                yield "              <div class=\"w-full h-full bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center\">
                <span class=\"text-xs font-bold text-gray-600\">LOGO</span>
              </div>
            ";
            }
            // line 104
            yield "          </div>
        ";
        } elseif (CoreExtension::getAttribute($this->env, $this->source,         // line 105
($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 105)) {
            // line 106
            yield "          <div class=\"w-12 h-12 bg-white rounded-md overflow-hidden border\">
            ";
            // line 107
            if (CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v6 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 107)) && is_array($_v6) || $_v6 instanceof ArrayAccess && in_array($_v6::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v6["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 107), "#items", [], "array", false, false, true, 107)), 0, [], "any", false, false, true, 107), "entity", [], "any", false, false, true, 107), "field_product_images", [], "any", false, false, true, 107)) {
                // line 108
                yield "              <img src=\"";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v7 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 108)) && is_array($_v7) || $_v7 instanceof ArrayAccess && in_array($_v7::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v7["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 108), "#items", [], "array", false, false, true, 108)), 0, [], "any", false, false, true, 108), "entity", [], "any", false, false, true, 108), "field_product_images", [], "any", false, false, true, 108), "entity", [], "any", false, false, true, 108), "uri", [], "any", false, false, true, 108), "value", [], "any", false, false, true, 108), "html", null, true);
                yield "\" alt=\"Product Image\" class=\"w-full h-full object-cover\">
            ";
            } else {
                // line 110
                yield "              <div class=\"w-full h-full bg-gradient-to-br from-primary-100 to-secondary-100 flex items-center justify-center\">
                <span class=\"text-xs font-bold text-gray-600\">IMG</span>
              </div>
            ";
            }
            // line 114
            yield "          </div>
        ";
        }
        // line 116
        yield "
        <div class=\"flex-1\">
          ";
        // line 119
        yield "          ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 119)) {
            // line 120
            yield "            <div class=\"font-medium text-gray-900\">
              ";
            // line 121
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v8 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 121)) && is_array($_v8) || $_v8 instanceof ArrayAccess && in_array($_v8::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v8["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_brand", [], "any", false, false, true, 121), "#items", [], "array", false, false, true, 121)), 0, [], "any", false, false, true, 121), "entity", [], "any", false, false, true, 121), "title", [], "any", false, false, true, 121), "value", [], "any", false, false, true, 121), "html", null, true);
            yield "
            </div>
            <div class=\"text-sm text-gray-500\">品牌</div>
          ";
        } elseif (CoreExtension::getAttribute($this->env, $this->source,         // line 124
($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 124)) {
            // line 125
            yield "            <div class=\"font-medium text-gray-900\">
              ";
            // line 126
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v9 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 126)) && is_array($_v9) || $_v9 instanceof ArrayAccess && in_array($_v9::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v9["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_product", [], "any", false, false, true, 126), "#items", [], "array", false, false, true, 126)), 0, [], "any", false, false, true, 126), "entity", [], "any", false, false, true, 126), "title", [], "any", false, false, true, 126), "value", [], "any", false, false, true, 126), "html", null, true);
            yield "
            </div>
            <div class=\"text-sm text-gray-500\">产品</div>
          ";
        } elseif (CoreExtension::getAttribute($this->env, $this->source,         // line 129
($context["content"] ?? null), "field_review_designer", [], "any", false, false, true, 129)) {
            // line 130
            yield "            <div class=\"font-medium text-gray-900\">
              ";
            // line 131
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v10 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_designer", [], "any", false, false, true, 131)) && is_array($_v10) || $_v10 instanceof ArrayAccess && in_array($_v10::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v10["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_designer", [], "any", false, false, true, 131), "#items", [], "array", false, false, true, 131)), 0, [], "any", false, false, true, 131), "entity", [], "any", false, false, true, 131), "title", [], "any", false, false, true, 131), "value", [], "any", false, false, true, 131), "html", null, true);
            yield "
            </div>
            <div class=\"text-sm text-gray-500\">设计师</div>
          ";
        }
        // line 135
        yield "        </div>

        ";
        // line 138
        yield "        <a href=\"#\" class=\"text-primary-600 hover:text-primary-700 transition-colors duration-200\">
          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"/>
          </svg>
        </a>
      </div>
    </div>

    ";
        // line 147
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_tags", [], "any", false, false, true, 147)) {
            // line 148
            yield "      <div class=\"review-tags mb-4\">
        <div class=\"flex flex-wrap gap-1\">
          ";
            // line 150
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((($_v11 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_tags", [], "any", false, false, true, 150)) && is_array($_v11) || $_v11 instanceof ArrayAccess && in_array($_v11::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v11["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_tags", [], "any", false, false, true, 150), "#items", [], "array", false, false, true, 150)));
            foreach ($context['_seq'] as $context["_key"] => $context["tag"]) {
                // line 151
                yield "            <span class=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\">
              #";
                // line 152
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["tag"], "entity", [], "any", false, false, true, 152), "name", [], "any", false, false, true, 152), "value", [], "any", false, false, true, 152), "html", null, true);
                yield "
            </span>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['tag'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 155
            yield "        </div>
      </div>
    ";
        }
        // line 158
        yield "
    ";
        // line 160
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_experience_date", [], "any", false, false, true, 160)) {
            // line 161
            yield "      <div class=\"experience-date flex items-center mb-4\">
        <svg class=\"w-4 h-4 text-gray-500 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"/>
        </svg>
        <span class=\"text-sm text-gray-600\">
          体验时间：";
            // line 166
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v12 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_experience_date", [], "any", false, false, true, 166)) && is_array($_v12) || $_v12 instanceof ArrayAccess && in_array($_v12::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v12["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_experience_date", [], "any", false, false, true, 166), "#items", [], "array", false, false, true, 166)), 0, [], "any", false, false, true, 166), "value", [], "any", false, false, true, 166), "Y年m月"), "html", null, true);
            yield "
        </span>
      </div>
    ";
        }
        // line 170
        yield "
    ";
        // line 172
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_recommendation", [], "any", false, false, true, 172)) {
            // line 173
            yield "      <div class=\"recommendation mb-4\">
        ";
            // line 174
            if (CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v13 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_recommendation", [], "any", false, false, true, 174)) && is_array($_v13) || $_v13 instanceof ArrayAccess && in_array($_v13::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v13["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_recommendation", [], "any", false, false, true, 174), "#items", [], "array", false, false, true, 174)), 0, [], "any", false, false, true, 174), "value", [], "any", false, false, true, 174)) {
                // line 175
                yield "          <div class=\"flex items-center text-green-600\">
            <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5\"/>
            </svg>
            <span class=\"font-medium\">推荐</span>
          </div>
        ";
            } else {
                // line 182
                yield "          <div class=\"flex items-center text-red-600\">
            <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7.5 15h2.25m8.25-9.75a3 3 0 11-6 0 3 3 0 016 0zM12.75 18a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z\"/>
            </svg>
            <span class=\"font-medium\">不推荐</span>
          </div>
        ";
            }
            // line 189
            yield "      </div>
    ";
        }
        // line 191
        yield "
    ";
        // line 193
        yield "    <div class=\"review-actions flex items-center justify-between pt-4 border-t border-gray-200\">
      <div class=\"flex items-center space-x-4\">
        ";
        // line 196
        yield "        <button class=\"helpful-btn flex items-center text-gray-500 hover:text-primary-600 transition-colors duration-200\">
          <svg class=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5\"/>
          </svg>
          <span class=\"text-sm\">
            有用 (";
        // line 201
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_helpful_votes", [], "any", false, true, true, 201), "#items", [], "array", false, true, true, 201), 0, [], "any", false, true, true, 201), "value", [], "any", true, true, true, 201)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v14 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_helpful_votes", [], "any", false, false, true, 201)) && is_array($_v14) || $_v14 instanceof ArrayAccess && in_array($_v14::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v14["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_review_helpful_votes", [], "any", false, false, true, 201), "#items", [], "array", false, false, true, 201)), 0, [], "any", false, false, true, 201), "value", [], "any", false, false, true, 201), "0")) : ("0")), "html", null, true);
        yield ")
          </span>
        </button>

        ";
        // line 206
        yield "        <button class=\"reply-btn flex items-center text-gray-500 hover:text-primary-600 transition-colors duration-200\">
          <svg class=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\"/>
          </svg>
          <span class=\"text-sm\">回复</span>
        </button>
      </div>

      ";
        // line 215
        yield "      <button class=\"share-btn p-2 text-gray-500 hover:text-primary-600 transition-colors duration-200\">
        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\"/>
        </svg>
      </button>
    </div>
  </div>

</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["attributes", "content", "node"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/content/node--review.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  424 => 215,  414 => 206,  407 => 201,  400 => 196,  396 => 193,  393 => 191,  389 => 189,  380 => 182,  371 => 175,  369 => 174,  366 => 173,  363 => 172,  360 => 170,  353 => 166,  346 => 161,  343 => 160,  340 => 158,  335 => 155,  326 => 152,  323 => 151,  319 => 150,  315 => 148,  312 => 147,  302 => 138,  298 => 135,  291 => 131,  288 => 130,  286 => 129,  280 => 126,  277 => 125,  275 => 124,  269 => 121,  266 => 120,  263 => 119,  259 => 116,  255 => 114,  249 => 110,  243 => 108,  241 => 107,  238 => 106,  236 => 105,  233 => 104,  227 => 100,  221 => 98,  219 => 97,  216 => 96,  213 => 95,  207 => 90,  204 => 88,  199 => 85,  190 => 82,  187 => 81,  183 => 80,  179 => 78,  176 => 77,  173 => 75,  167 => 72,  164 => 71,  161 => 70,  158 => 68,  151 => 64,  147 => 62,  144 => 61,  140 => 58,  133 => 54,  129 => 52,  118 => 48,  114 => 47,  110 => 45,  107 => 44,  99 => 38,  93 => 34,  87 => 30,  84 => 29,  79 => 25,  72 => 21,  68 => 19,  62 => 17,  60 => 16,  57 => 15,  53 => 12,  47 => 8,  44 => 7,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/content/node--review.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/content/node--review.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["if" => 16, "for" => 47];
        static $filters = ["escape" => 8, "upper" => 21, "first" => 21, "date" => 38, "slice" => 80, "default" => 201];
        static $functions = ["range" => 47];

        try {
            $this->sandbox->checkSecurity(
                ['if', 'for'],
                ['escape', 'upper', 'first', 'date', 'slice', 'default'],
                ['range'],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
