<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/layout/page.html.twig */
class __TwigTemplate_a7a2a19d01e539512397c699dc023872 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 8
        yield "<div class=\"min-h-screen bg-gray-50\">

  ";
        // line 11
        yield "  <nav class=\"bg-white shadow-sm border-b border-gray-200\" role=\"navigation\" aria-label=\"主导航\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">
      <div class=\"flex justify-between items-center h-16\">

        ";
        // line 16
        yield "        <div class=\"flex items-center\">
          <div class=\"flex-shrink-0\">
            <a href=\"/\" class=\"flex items-center group\" aria-label=\"回到首页\">
              <div class=\"w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center mr-3 transition-transform duration-200 group-hover:scale-105\">
                <span class=\"text-white font-bold text-lg\">J</span>
              </div>
              <div class=\"flex flex-col\">
                <span class=\"text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-200\">Juyin</span>
                <span class=\"text-xs text-gray-500 -mt-1\">意大利家居</span>
              </div>
            </a>
          </div>
        </div>

        ";
        // line 31
        yield "        <div class=\"hidden md:block\">
          <div class=\"ml-10 flex items-baseline space-x-8\">
            <a href=\"/\" class=\"px-3 py-2 text-sm font-medium transition-colors duration-200 rounded-md text-primary-600 bg-primary-50\">首页</a>
            <a href=\"/brands\" class=\"px-3 py-2 text-sm font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\">品牌</a>
            <a href=\"/products\" class=\"px-3 py-2 text-sm font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\">产品</a>
            <a href=\"/designers\" class=\"px-3 py-2 text-sm font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\">设计师</a>
            <a href=\"/news\" class=\"px-3 py-2 text-sm font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\">资讯</a>
            <a href=\"/reviews\" class=\"px-3 py-2 text-sm font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\">评价</a>
          </div>
        </div>

        ";
        // line 43
        yield "        <div class=\"flex items-center space-x-4\">

          ";
        // line 46
        yield "          <div class=\"relative hidden md:block\">
            <div class=\"relative\">
              <input type=\"text\"
                     id=\"search-input\"
                     placeholder=\"搜索产品、品牌、设计师...\"
                     class=\"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg
                            focus:ring-2 focus:ring-primary-500 focus:border-primary-500
                            text-sm transition-all duration-200
                            hover:border-gray-400\"
                     aria-label=\"搜索\">
              <div class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">
                <svg class=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">
                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"/>
                </svg>
              </div>
            </div>
          </div>

          ";
        // line 65
        yield "          <button class=\"md:hidden p-2 text-gray-500 hover:text-primary-600 transition-colors duration-200 rounded-md hover:bg-gray-50\"
                  id=\"mobile-search-btn\"
                  aria-label=\"打开搜索\">
            <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"/>
            </svg>
          </button>

          ";
        // line 74
        yield "          <div class=\"relative\">
            <button class=\"flex items-center text-sm text-gray-500 hover:text-primary-600 transition-colors duration-200 px-2 py-1 rounded-md hover:bg-gray-50\"
                    aria-label=\"切换语言\">
              <span class=\"mr-1\">中文</span>
              <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">
                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/>
              </svg>
            </button>
          </div>

          ";
        // line 85
        yield "          <div class=\"relative\">
            <button class=\"flex items-center text-sm text-gray-500 hover:text-primary-600 transition-colors duration-200 p-2 rounded-md hover:bg-gray-50\"
                    aria-label=\"用户菜单\">
              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">
                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"/>
              </svg>
            </button>
          </div>

          ";
        // line 95
        yield "          <div class=\"md:hidden\">
            <button type=\"button\"
                    id=\"mobile-menu-button\"
                    class=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200\"
                    aria-controls=\"mobile-menu\"
                    aria-expanded=\"false\"
                    aria-label=\"打开主菜单\">
              <span class=\"sr-only\">打开主菜单</span>
              <svg id=\"menu-open-icon\" class=\"block h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">
                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />
              </svg>
              <svg id=\"menu-close-icon\" class=\"hidden h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">
                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    ";
        // line 116
        yield "    <div id=\"mobile-menu\" class=\"md:hidden hidden\" role=\"menu\">
      <div class=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg\">
        <a href=\"/\" class=\"block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md text-primary-600 bg-primary-50\" role=\"menuitem\">首页</a>
        <a href=\"/brands\" class=\"block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\" role=\"menuitem\">品牌</a>
        <a href=\"/products\" class=\"block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\" role=\"menuitem\">产品</a>
        <a href=\"/designers\" class=\"block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\" role=\"menuitem\">设计师</a>
        <a href=\"/news\" class=\"block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\" role=\"menuitem\">资讯</a>
        <a href=\"/reviews\" class=\"block px-3 py-2 text-base font-medium transition-colors duration-200 rounded-md text-gray-500 hover:text-primary-600 hover:bg-gray-50\" role=\"menuitem\">评价</a>
      </div>
    </div>
  </nav>

  ";
        // line 129
        yield "  ";
        if ((CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "header", [], "any", false, false, true, 129) && false)) {
            // line 130
            yield "    <header class=\"bg-white shadow-sm\">
      <div class=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">
        ";
            // line 132
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "header", [], "any", false, false, true, 132), "html", null, true);
            yield "
      </div>
    </header>
  ";
        }
        // line 136
        yield "
  ";
        // line 138
        yield "  ";
        if ((CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "primary_menu", [], "any", false, false, true, 138) && false)) {
            // line 139
            yield "    <div class=\"bg-white border-b border-gray-200\">
      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">
        ";
            // line 141
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "primary_menu", [], "any", false, false, true, 141), "html", null, true);
            yield "
      </div>
    </div>
  ";
        }
        // line 145
        yield "
  ";
        // line 147
        yield "  ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "breadcrumb", [], "any", false, false, true, 147)) {
            // line 148
            yield "    <div class=\"bg-white border-b border-gray-200\">
      <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3\">
        ";
            // line 150
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "breadcrumb", [], "any", false, false, true, 150), "html", null, true);
            yield "
      </div>
    </div>
  ";
        }
        // line 154
        yield "
  ";
        // line 156
        yield "  <main class=\"flex-1\">
    <div class=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">

      ";
        // line 160
        yield "      ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "highlighted", [], "any", false, false, true, 160)) {
            // line 161
            yield "        <div class=\"mb-6\">
          ";
            // line 162
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "highlighted", [], "any", false, false, true, 162), "html", null, true);
            yield "
        </div>
      ";
        }
        // line 165
        yield "
      <div class=\"flex flex-col lg:flex-row gap-8\">

        ";
        // line 169
        yield "        ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 169)) {
            // line 170
            yield "          <aside class=\"lg:w-64 flex-shrink-0\">
            <div class=\"bg-white rounded-lg shadow-soft p-6\">
              ";
            // line 172
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_first", [], "any", false, false, true, 172), "html", null, true);
            yield "
            </div>
          </aside>
        ";
        }
        // line 176
        yield "
        ";
        // line 178
        yield "        <div class=\"flex-1 min-w-0\">
          ";
        // line 179
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "content", [], "any", false, false, true, 179), "html", null, true);
        yield "
        </div>

        ";
        // line 183
        yield "        ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 183)) {
            // line 184
            yield "          <aside class=\"lg:w-80 flex-shrink-0\">
            <div class=\"bg-white rounded-lg shadow-soft p-6\">
              ";
            // line 186
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "sidebar_second", [], "any", false, false, true, 186), "html", null, true);
            yield "
            </div>
          </aside>
        ";
        }
        // line 190
        yield "      </div>
    </div>
  </main>

  ";
        // line 195
        yield "  <footer class=\"bg-gray-900 text-white mt-16\">
    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">

      ";
        // line 199
        yield "      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">

        ";
        // line 202
        yield "        <div class=\"col-span-1 lg:col-span-2\">
          <div class=\"flex items-center mb-4\">
            <div class=\"w-8 h-8 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center mr-3\">
              <span class=\"text-white font-bold text-lg\">J</span>
            </div>
            <span class=\"text-xl font-bold\">Juyin</span>
            <span class=\"text-sm text-gray-400 ml-2\">意大利家居</span>
          </div>
          <p class=\"text-gray-400 mb-4 max-w-md\">
            专注于意大利高端家居产品，为您提供最优质的家具、设计师作品和家居资讯。
          </p>
          <div class=\"flex space-x-4\">
            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">
              <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">
                <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>
              </svg>
            </a>
            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">
              <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">
                <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>
              </svg>
            </a>
            <a href=\"#\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">
              <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">
                <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z\"/>
              </svg>
            </a>
          </div>
        </div>

        ";
        // line 233
        yield "        <div>
          <h3 class=\"text-lg font-semibold mb-4\">快速链接</h3>
          <ul class=\"space-y-2\">
            <li><a href=\"/about\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">关于我们</a></li>
            <li><a href=\"/contact\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">联系我们</a></li>
            <li><a href=\"/careers\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">招聘信息</a></li>
            <li><a href=\"/press\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">媒体中心</a></li>
          </ul>
        </div>

        ";
        // line 244
        yield "        <div>
          <h3 class=\"text-lg font-semibold mb-4\">客户服务</h3>
          <ul class=\"space-y-2\">
            <li><a href=\"/help\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">帮助中心</a></li>
            <li><a href=\"/shipping\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">配送信息</a></li>
            <li><a href=\"/returns\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">退换货政策</a></li>
            <li><a href=\"/privacy\" class=\"text-gray-400 hover:text-white transition-colors duration-200\">隐私政策</a></li>
          </ul>
        </div>
      </div>

      ";
        // line 256
        yield "      <div class=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">
        <p class=\"text-gray-400 text-sm\">
          © 2024 Juyin 意大利家居. 保留所有权利.
        </p>
        <div class=\"flex items-center space-x-4 mt-4 md:mt-0\">
          <span class=\"text-gray-400 text-sm\">Powered by</span>
          <span class=\"text-primary-400 font-semibold\">Drupal 10</span>
        </div>
      </div>

      ";
        // line 267
        yield "      ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "footer", [], "any", false, false, true, 267)) {
            // line 268
            yield "        <div class=\"mt-8\">
          ";
            // line 269
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["page"] ?? null), "footer", [], "any", false, false, true, 269), "html", null, true);
            yield "
        </div>
      ";
        }
        // line 272
        yield "    </div>
  </footer>

</div>

";
        // line 278
        yield "<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  const menuOpenIcon = document.getElementById('menu-open-icon');
  const menuCloseIcon = document.getElementById('menu-close-icon');

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';

      // Toggle menu visibility
      mobileMenu.classList.toggle('hidden');

      // Toggle icons
      menuOpenIcon.classList.toggle('hidden');
      menuCloseIcon.classList.toggle('hidden');

      // Update aria-expanded
      mobileMenuButton.setAttribute('aria-expanded', !isExpanded);
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
      if (!mobileMenuButton.contains(event.target) && !mobileMenu.contains(event.target)) {
        mobileMenu.classList.add('hidden');
        menuOpenIcon.classList.remove('hidden');
        menuCloseIcon.classList.add('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'false');
      }
    });

    // Close menu on window resize to desktop size
    window.addEventListener('resize', function() {
      if (window.innerWidth >= 768) { // md breakpoint
        mobileMenu.classList.add('hidden');
        menuOpenIcon.classList.remove('hidden');
        menuCloseIcon.classList.add('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'false');
      }
    });
  }
});
</script>";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["page"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/layout/page.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  392 => 278,  385 => 272,  379 => 269,  376 => 268,  373 => 267,  361 => 256,  348 => 244,  336 => 233,  304 => 202,  300 => 199,  295 => 195,  289 => 190,  282 => 186,  278 => 184,  275 => 183,  269 => 179,  266 => 178,  263 => 176,  256 => 172,  252 => 170,  249 => 169,  244 => 165,  238 => 162,  235 => 161,  232 => 160,  227 => 156,  224 => 154,  217 => 150,  213 => 148,  210 => 147,  207 => 145,  200 => 141,  196 => 139,  193 => 138,  190 => 136,  183 => 132,  179 => 130,  176 => 129,  162 => 116,  140 => 95,  129 => 85,  117 => 74,  107 => 65,  87 => 46,  83 => 43,  70 => 31,  54 => 16,  48 => 11,  44 => 8,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/layout/page.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/layout/page.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["if" => 129];
        static $filters = ["escape" => 132];
        static $functions = [];

        try {
            $this->sandbox->checkSecurity(
                ['if'],
                ['escape'],
                [],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
