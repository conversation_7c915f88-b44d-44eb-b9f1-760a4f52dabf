<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/content/node--news.html.twig */
class __TwigTemplate_d037af255660683ab5d0c2b52da547a8 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 7
        yield "
<article";
        // line 8
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", ["news-card", "bg-white", "rounded-lg", "shadow-soft", "overflow-hidden", "hover:shadow-medium", "transition-shadow", "duration-300", "group"], "method", false, false, true, 8), "html", null, true);
        yield ">
  
  ";
        // line 11
        yield "  ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_image", [], "any", false, false, true, 11)) {
            // line 12
            yield "    <div class=\"news-image aspect-w-16 aspect-h-9 bg-gray-100 relative overflow-hidden\">
      ";
            // line 13
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_image", [], "any", false, false, true, 13), "html", null, true);
            yield "
      
      ";
            // line 16
            yield "      ";
            if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_category", [], "any", false, false, true, 16)) {
                // line 17
                yield "        <div class=\"absolute top-3 left-3\">
          <span class=\"inline-block bg-primary-600 text-white text-xs px-3 py-1 rounded-full font-medium\">
            ";
                // line 19
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_category", [], "any", false, false, true, 19), "html", null, true);
                yield "
          </span>
        </div>
      ";
            }
            // line 23
            yield "
      ";
            // line 25
            yield "      <div class=\"absolute top-3 right-3\">
        <span class=\"inline-block bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded-full\">
          ";
            // line 27
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_reading_time", [], "any", false, true, true, 27), "#items", [], "array", false, true, true, 27), 0, [], "any", false, true, true, 27), "value", [], "any", true, true, true, 27)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v0 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_reading_time", [], "any", false, false, true, 27)) && is_array($_v0) || $_v0 instanceof ArrayAccess && in_array($_v0::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v0["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_reading_time", [], "any", false, false, true, 27), "#items", [], "array", false, false, true, 27)), 0, [], "any", false, false, true, 27), "value", [], "any", false, false, true, 27), "5")) : ("5")), "html", null, true);
            yield " 分钟阅读
        </span>
      </div>
    </div>
  ";
        }
        // line 32
        yield "
  <div class=\"p-6\">
    ";
        // line 35
        yield "    <div class=\"news-meta flex items-center justify-between mb-3\">
      <div class=\"flex items-center space-x-4\">
        ";
        // line 38
        yield "        ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_author", [], "any", false, false, true, 38)) {
            // line 39
            yield "          <div class=\"flex items-center\">
            <svg class=\"w-4 h-4 text-gray-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"/>
            </svg>
            <span class=\"text-sm text-gray-600\">
              ";
            // line 44
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_author", [], "any", false, false, true, 44), "html", null, true);
            yield "
            </span>
          </div>
        ";
        }
        // line 48
        yield "
        ";
        // line 50
        yield "        <div class=\"flex items-center\">
          <svg class=\"w-4 h-4 text-gray-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"/>
          </svg>
          <span class=\"text-sm text-gray-600\">
            ";
        // line 55
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, $this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["node"] ?? null), "created", [], "any", false, false, true, 55), "value", [], "any", false, false, true, 55), "Y-m-d"), "html", null, true);
        yield "
          </span>
        </div>
      </div>

      ";
        // line 61
        yield "      ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_source", [], "any", false, false, true, 61)) {
            // line 62
            yield "        <div class=\"news-source\">
          <span class=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">
            ";
            // line 64
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_source", [], "any", false, false, true, 64), "html", null, true);
            yield "
          </span>
        </div>
      ";
        }
        // line 68
        yield "    </div>

    ";
        // line 71
        yield "    <h3 class=\"text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-primary-600 transition-colors duration-200\">
      <a href=\"";
        // line 72
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"hover:text-primary-600 transition-colors duration-200\">
        ";
        // line 73
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["label"] ?? null), "html", null, true);
        yield "
      </a>
    </h3>

    ";
        // line 78
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_summary", [], "any", false, false, true, 78)) {
            // line 79
            yield "      <div class=\"news-summary text-gray-600 text-sm mb-4 line-clamp-3\">
        ";
            // line 80
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_summary", [], "any", false, false, true, 80), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 83
        yield "
    ";
        // line 85
        yield "    ";
        if ((CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_brands", [], "any", false, false, true, 85) || CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_products", [], "any", false, false, true, 85))) {
            // line 86
            yield "      <div class=\"related-items mb-4\">
        <h4 class=\"text-sm font-medium text-gray-700 mb-2\">相关内容</h4>
        <div class=\"flex flex-wrap gap-2\">
          ";
            // line 89
            if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_brands", [], "any", false, false, true, 89)) {
                // line 90
                yield "            ";
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (($_v1 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_brands", [], "any", false, false, true, 90)) && is_array($_v1) || $_v1 instanceof ArrayAccess && in_array($_v1::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v1["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_brands", [], "any", false, false, true, 90), "#items", [], "array", false, false, true, 90)), 0, 2));
                foreach ($context['_seq'] as $context["_key"] => $context["brand"]) {
                    // line 91
                    yield "              <a href=\"/node/";
                    yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, $context["brand"], "target_id", [], "any", false, false, true, 91), "html", null, true);
                    yield "\" class=\"inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full hover:bg-blue-200 transition-colors duration-200\">
                ";
                    // line 92
                    yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["brand"], "entity", [], "any", false, false, true, 92), "title", [], "any", false, false, true, 92), "value", [], "any", false, false, true, 92), "html", null, true);
                    yield "
              </a>
            ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['brand'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 95
                yield "          ";
            }
            // line 96
            yield "          ";
            if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_products", [], "any", false, false, true, 96)) {
                // line 97
                yield "            ";
                $context['_parent'] = $context;
                $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (($_v2 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_products", [], "any", false, false, true, 97)) && is_array($_v2) || $_v2 instanceof ArrayAccess && in_array($_v2::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v2["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_related_products", [], "any", false, false, true, 97), "#items", [], "array", false, false, true, 97)), 0, 2));
                foreach ($context['_seq'] as $context["_key"] => $context["product"]) {
                    // line 98
                    yield "              <a href=\"/node/";
                    yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, $context["product"], "target_id", [], "any", false, false, true, 98), "html", null, true);
                    yield "\" class=\"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full hover:bg-green-200 transition-colors duration-200\">
                ";
                    // line 99
                    yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["product"], "entity", [], "any", false, false, true, 99), "title", [], "any", false, false, true, 99), "value", [], "any", false, false, true, 99), "html", null, true);
                    yield "
              </a>
            ";
                }
                $_parent = $context['_parent'];
                unset($context['_seq'], $context['_key'], $context['product'], $context['_parent']);
                $context = array_intersect_key($context, $_parent) + $_parent;
                // line 102
                yield "          ";
            }
            // line 103
            yield "        </div>
      </div>
    ";
        }
        // line 106
        yield "
    ";
        // line 108
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_tags", [], "any", false, false, true, 108)) {
            // line 109
            yield "      <div class=\"news-tags mb-4\">
        <div class=\"flex flex-wrap gap-1\">
          ";
            // line 111
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (($_v3 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_tags", [], "any", false, false, true, 111)) && is_array($_v3) || $_v3 instanceof ArrayAccess && in_array($_v3::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v3["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_news_tags", [], "any", false, false, true, 111), "#items", [], "array", false, false, true, 111)), 0, 3));
            foreach ($context['_seq'] as $context["_key"] => $context["tag"]) {
                // line 112
                yield "            <span class=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\">
              #";
                // line 113
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["tag"], "entity", [], "any", false, false, true, 113), "name", [], "any", false, false, true, 113), "value", [], "any", false, false, true, 113), "html", null, true);
                yield "
            </span>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['tag'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 116
            yield "        </div>
      </div>
    ";
        }
        // line 119
        yield "
    ";
        // line 121
        yield "    <div class=\"engagement-stats flex items-center justify-between mb-4\">
      <div class=\"flex items-center space-x-4\">
        ";
        // line 124
        yield "        <div class=\"flex items-center\">
          <svg class=\"w-4 h-4 text-gray-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"/>
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"/>
          </svg>
          <span class=\"text-sm text-gray-600\">
            ";
        // line 130
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_view_count", [], "any", false, true, true, 130), "#items", [], "array", false, true, true, 130), 0, [], "any", false, true, true, 130), "value", [], "any", true, true, true, 130)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v4 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_view_count", [], "any", false, false, true, 130)) && is_array($_v4) || $_v4 instanceof ArrayAccess && in_array($_v4::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v4["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_view_count", [], "any", false, false, true, 130), "#items", [], "array", false, false, true, 130)), 0, [], "any", false, false, true, 130), "value", [], "any", false, false, true, 130), "128")) : ("128")), "html", null, true);
        yield "
          </span>
        </div>

        ";
        // line 135
        yield "        <div class=\"flex items-center\">
          <svg class=\"w-4 h-4 text-gray-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"/>
          </svg>
          <span class=\"text-sm text-gray-600\">
            ";
        // line 140
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "comment_count", [], "any", true, true, true, 140)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "comment_count", [], "any", false, false, true, 140), "5")) : ("5")), "html", null, true);
        yield "
          </span>
        </div>

        ";
        // line 145
        yield "        <div class=\"flex items-center\">
          <svg class=\"w-4 h-4 text-gray-500 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"/>
          </svg>
          <span class=\"text-sm text-gray-600\">
            ";
        // line 150
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_like_count", [], "any", false, true, true, 150), "#items", [], "array", false, true, true, 150), 0, [], "any", false, true, true, 150), "value", [], "any", true, true, true, 150)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v5 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_like_count", [], "any", false, false, true, 150)) && is_array($_v5) || $_v5 instanceof ArrayAccess && in_array($_v5::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v5["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_like_count", [], "any", false, false, true, 150), "#items", [], "array", false, false, true, 150)), 0, [], "any", false, false, true, 150), "value", [], "any", false, false, true, 150), "23")) : ("23")), "html", null, true);
        yield "
          </span>
        </div>
      </div>

      ";
        // line 156
        yield "      <button class=\"share-btn p-2 text-gray-500 hover:text-primary-600 transition-colors duration-200\">
        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z\"/>
        </svg>
      </button>
    </div>

    ";
        // line 164
        yield "    <div class=\"flex space-x-3\">
      <a href=\"";
        // line 165
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"flex-1 bg-primary-600 text-white text-center py-2 px-4 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium\">
        阅读全文
      </a>
      <button class=\"bookmark-btn p-2 border border-gray-300 rounded-md hover:border-primary-600 hover:text-primary-600 transition-colors duration-200\">
        <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"/>
        </svg>
      </button>
    </div>
  </div>

</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["attributes", "content", "node", "url", "label"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/content/node--news.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  338 => 165,  335 => 164,  326 => 156,  318 => 150,  311 => 145,  304 => 140,  297 => 135,  290 => 130,  282 => 124,  278 => 121,  275 => 119,  270 => 116,  261 => 113,  258 => 112,  254 => 111,  250 => 109,  247 => 108,  244 => 106,  239 => 103,  236 => 102,  227 => 99,  222 => 98,  217 => 97,  214 => 96,  211 => 95,  202 => 92,  197 => 91,  192 => 90,  190 => 89,  185 => 86,  182 => 85,  179 => 83,  173 => 80,  170 => 79,  167 => 78,  160 => 73,  156 => 72,  153 => 71,  149 => 68,  142 => 64,  138 => 62,  135 => 61,  127 => 55,  120 => 50,  117 => 48,  110 => 44,  103 => 39,  100 => 38,  96 => 35,  92 => 32,  84 => 27,  80 => 25,  77 => 23,  70 => 19,  66 => 17,  63 => 16,  58 => 13,  55 => 12,  52 => 11,  47 => 8,  44 => 7,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/content/node--news.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/content/node--news.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["if" => 11, "for" => 90];
        static $filters = ["escape" => 8, "default" => 27, "date" => 55, "slice" => 90];
        static $functions = [];

        try {
            $this->sandbox->checkSecurity(
                ['if', 'for'],
                ['escape', 'default', 'date', 'slice'],
                [],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
