<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* themes/custom/juyin/templates/content/node--designer.html.twig */
class __TwigTemplate_42456ed20c00e1e93cb4d6708a128ab2 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 7
        yield "
<article";
        // line 8
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["attributes"] ?? null), "addClass", ["designer-card", "bg-white", "rounded-lg", "shadow-soft", "overflow-hidden", "hover:shadow-medium", "transition-shadow", "duration-300", "group"], "method", false, false, true, 8), "html", null, true);
        yield ">
  
  ";
        // line 11
        yield "  ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_avatar", [], "any", false, false, true, 11)) {
            // line 12
            yield "    <div class=\"designer-avatar relative\">
      <div class=\"aspect-w-1 aspect-h-1 bg-gradient-to-br from-primary-100 to-secondary-100\">
        ";
            // line 14
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_avatar", [], "any", false, false, true, 14), "html", null, true);
            yield "
      </div>
      
      ";
            // line 18
            yield "      <div class=\"absolute bottom-3 right-3\">
        <div class=\"w-4 h-4 bg-green-400 border-2 border-white rounded-full\"></div>
      </div>
    </div>
  ";
        }
        // line 23
        yield "
  <div class=\"p-6\">
    ";
        // line 26
        yield "    <h3 class=\"text-xl font-bold text-gray-900 mb-2\">
      <a href=\"";
        // line 27
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"hover:text-primary-600 transition-colors duration-200\">
        ";
        // line 28
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["label"] ?? null), "html", null, true);
        yield "
      </a>
    </h3>

    ";
        // line 33
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_specialties", [], "any", false, false, true, 33)) {
            // line 34
            yield "      <div class=\"designer-specialties mb-3\">
        <div class=\"flex flex-wrap gap-1\">
          ";
            // line 36
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((($_v0 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_specialties", [], "any", false, false, true, 36)) && is_array($_v0) || $_v0 instanceof ArrayAccess && in_array($_v0::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v0["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_specialties", [], "any", false, false, true, 36), "#items", [], "array", false, false, true, 36)));
            foreach ($context['_seq'] as $context["_key"] => $context["specialty"]) {
                // line 37
                yield "            <span class=\"inline-block bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full\">
              ";
                // line 38
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["specialty"], "entity", [], "any", false, false, true, 38), "name", [], "any", false, false, true, 38), "value", [], "any", false, false, true, 38), "html", null, true);
                yield "
            </span>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['specialty'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 41
            yield "        </div>
      </div>
    ";
        }
        // line 44
        yield "
    ";
        // line 46
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_bio", [], "any", false, false, true, 46)) {
            // line 47
            yield "      <div class=\"designer-bio text-gray-600 text-sm mb-4 line-clamp-3\">
        ";
            // line 48
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_bio", [], "any", false, false, true, 48), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 51
        yield "
    ";
        // line 53
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_experience", [], "any", false, false, true, 53)) {
            // line 54
            yield "      <div class=\"designer-experience flex items-center mb-3\">
        <svg class=\"w-4 h-4 text-gray-500 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>
        </svg>
        <span class=\"text-sm text-gray-600\">
          ";
            // line 59
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v1 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_experience", [], "any", false, false, true, 59)) && is_array($_v1) || $_v1 instanceof ArrayAccess && in_array($_v1::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v1["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_experience", [], "any", false, false, true, 59), "#items", [], "array", false, false, true, 59)), 0, [], "any", false, false, true, 59), "value", [], "any", false, false, true, 59), "html", null, true);
            yield " 年经验
        </span>
      </div>
    ";
        }
        // line 63
        yield "
    ";
        // line 65
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_rating", [], "any", false, false, true, 65)) {
            // line 66
            yield "      <div class=\"designer-rating flex items-center mb-4\">
        <div class=\"flex items-center\">
          ";
            // line 68
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, 5));
            foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
                // line 69
                yield "            <svg class=\"w-4 h-4 ";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(((($context["i"] <= CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v2 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_rating", [], "any", false, false, true, 69)) && is_array($_v2) || $_v2 instanceof ArrayAccess && in_array($_v2::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v2["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_rating", [], "any", false, false, true, 69), "#items", [], "array", false, false, true, 69)), 0, [], "any", false, false, true, 69), "value", [], "any", false, false, true, 69))) ? ("text-yellow-400") : ("text-gray-300")));
                yield "\" fill=\"currentColor\" viewBox=\"0 0 20 20\">
              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"/>
            </svg>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 73
            yield "        </div>
        <span class=\"ml-2 text-sm text-gray-600\">
          (";
            // line 75
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v3 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_rating", [], "any", false, false, true, 75)) && is_array($_v3) || $_v3 instanceof ArrayAccess && in_array($_v3::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v3["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_rating", [], "any", false, false, true, 75), "#items", [], "array", false, false, true, 75)), 0, [], "any", false, false, true, 75), "value", [], "any", false, false, true, 75), "html", null, true);
            yield ")
        </span>
      </div>
    ";
        }
        // line 79
        yield "
    ";
        // line 81
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_styles", [], "any", false, false, true, 81)) {
            // line 82
            yield "      <div class=\"designer-styles mb-4\">
        <h4 class=\"text-sm font-medium text-gray-700 mb-2\">设计风格</h4>
        <div class=\"flex flex-wrap gap-1\">
          ";
            // line 85
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((($_v4 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_styles", [], "any", false, false, true, 85)) && is_array($_v4) || $_v4 instanceof ArrayAccess && in_array($_v4::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v4["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_styles", [], "any", false, false, true, 85), "#items", [], "array", false, false, true, 85)));
            foreach ($context['_seq'] as $context["_key"] => $context["style"]) {
                // line 86
                yield "            <span class=\"inline-block bg-accent-100 text-accent-800 text-xs px-2 py-1 rounded-full\">
              ";
                // line 87
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["style"], "entity", [], "any", false, false, true, 87), "name", [], "any", false, false, true, 87), "value", [], "any", false, false, true, 87), "html", null, true);
                yield "
            </span>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['style'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 90
            yield "        </div>
      </div>
    ";
        }
        // line 93
        yield "
    ";
        // line 95
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_locations", [], "any", false, false, true, 95)) {
            // line 96
            yield "      <div class=\"designer-location flex items-center mb-4\">
        <svg class=\"w-4 h-4 text-gray-500 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>
          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>
        </svg>
        <span class=\"text-sm text-gray-600\">
          ";
            // line 102
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_locations", [], "any", false, false, true, 102), "html", null, true);
            yield "
        </span>
      </div>
    ";
        }
        // line 106
        yield "
    ";
        // line 108
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_portfolio", [], "any", false, false, true, 108)) {
            // line 109
            yield "      <div class=\"portfolio-preview mb-4\">
        <h4 class=\"text-sm font-medium text-gray-700 mb-2\">作品集预览</h4>
        <div class=\"grid grid-cols-3 gap-2\">
          ";
            // line 112
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(Twig\Extension\CoreExtension::slice($this->env->getCharset(), (($_v5 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_portfolio", [], "any", false, false, true, 112)) && is_array($_v5) || $_v5 instanceof ArrayAccess && in_array($_v5::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v5["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_portfolio", [], "any", false, false, true, 112), "#items", [], "array", false, false, true, 112)), 0, 3));
            foreach ($context['_seq'] as $context["_key"] => $context["portfolio_item"]) {
                // line 113
                yield "            <div class=\"aspect-w-1 aspect-h-1 bg-gray-100 rounded-md overflow-hidden\">
              <img src=\"";
                // line 114
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, $context["portfolio_item"], "entity", [], "any", false, false, true, 114), "uri", [], "any", false, false, true, 114), "value", [], "any", false, false, true, 114), "html", null, true);
                yield "\" alt=\"Portfolio\" class=\"object-cover w-full h-full\">
            </div>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['portfolio_item'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 117
            yield "        </div>
      </div>
    ";
        }
        // line 120
        yield "
    ";
        // line 122
        yield "    <div class=\"flex space-x-3\">
      <a href=\"";
        // line 123
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ($context["url"] ?? null), "html", null, true);
        yield "\" class=\"flex-1 bg-primary-600 text-white text-center py-2 px-4 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium\">
        查看档案
      </a>
      <button class=\"contact-btn bg-secondary-100 text-secondary-700 py-2 px-4 rounded-md hover:bg-secondary-200 transition-colors duration-200 text-sm font-medium\">
        联系设计师
      </button>
    </div>

    ";
        // line 132
        yield "    <div class=\"grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200\">
      <div class=\"text-center\">
        <div class=\"text-lg font-bold text-gray-900\">
          ";
        // line 135
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_projects_count", [], "any", false, true, true, 135), "#items", [], "array", false, true, true, 135), 0, [], "any", false, true, true, 135), "value", [], "any", true, true, true, 135)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v6 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_projects_count", [], "any", false, false, true, 135)) && is_array($_v6) || $_v6 instanceof ArrayAccess && in_array($_v6::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v6["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_projects_count", [], "any", false, false, true, 135), "#items", [], "array", false, false, true, 135)), 0, [], "any", false, false, true, 135), "value", [], "any", false, false, true, 135), "12")) : ("12")), "html", null, true);
        yield "
        </div>
        <div class=\"text-xs text-gray-500\">项目</div>
      </div>
      <div class=\"text-center\">
        <div class=\"text-lg font-bold text-gray-900\">
          ";
        // line 141
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_clients_count", [], "any", false, true, true, 141), "#items", [], "array", false, true, true, 141), 0, [], "any", false, true, true, 141), "value", [], "any", true, true, true, 141)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v7 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_clients_count", [], "any", false, false, true, 141)) && is_array($_v7) || $_v7 instanceof ArrayAccess && in_array($_v7::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v7["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_clients_count", [], "any", false, false, true, 141), "#items", [], "array", false, false, true, 141)), 0, [], "any", false, false, true, 141), "value", [], "any", false, false, true, 141), "8")) : ("8")), "html", null, true);
        yield "
        </div>
        <div class=\"text-xs text-gray-500\">客户</div>
      </div>
      <div class=\"text-center\">
        <div class=\"text-lg font-bold text-gray-900\">
          ";
        // line 147
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_awards_count", [], "any", false, true, true, 147), "#items", [], "array", false, true, true, 147), 0, [], "any", false, true, true, 147), "value", [], "any", true, true, true, 147)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (($_v8 = CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_awards_count", [], "any", false, false, true, 147)) && is_array($_v8) || $_v8 instanceof ArrayAccess && in_array($_v8::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v8["#items"] ?? null) : CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["content"] ?? null), "field_designer_awards_count", [], "any", false, false, true, 147), "#items", [], "array", false, false, true, 147)), 0, [], "any", false, false, true, 147), "value", [], "any", false, false, true, 147), "3")) : ("3")), "html", null, true);
        yield "
        </div>
        <div class=\"text-xs text-gray-500\">奖项</div>
      </div>
    </div>
  </div>

</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["attributes", "content", "url", "label"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "themes/custom/juyin/templates/content/node--designer.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  314 => 147,  305 => 141,  296 => 135,  291 => 132,  280 => 123,  277 => 122,  274 => 120,  269 => 117,  260 => 114,  257 => 113,  253 => 112,  248 => 109,  245 => 108,  242 => 106,  235 => 102,  227 => 96,  224 => 95,  221 => 93,  216 => 90,  207 => 87,  204 => 86,  200 => 85,  195 => 82,  192 => 81,  189 => 79,  182 => 75,  178 => 73,  167 => 69,  163 => 68,  159 => 66,  156 => 65,  153 => 63,  146 => 59,  139 => 54,  136 => 53,  133 => 51,  127 => 48,  124 => 47,  121 => 46,  118 => 44,  113 => 41,  104 => 38,  101 => 37,  97 => 36,  93 => 34,  90 => 33,  83 => 28,  79 => 27,  76 => 26,  72 => 23,  65 => 18,  59 => 14,  55 => 12,  52 => 11,  47 => 8,  44 => 7,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "themes/custom/juyin/templates/content/node--designer.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/content/node--designer.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["if" => 11, "for" => 36];
        static $filters = ["escape" => 8, "slice" => 112, "default" => 135];
        static $functions = ["range" => 68];

        try {
            $this->sandbox->checkSecurity(
                ['if', 'for'],
                ['escape', 'slice', 'default'],
                ['range'],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
