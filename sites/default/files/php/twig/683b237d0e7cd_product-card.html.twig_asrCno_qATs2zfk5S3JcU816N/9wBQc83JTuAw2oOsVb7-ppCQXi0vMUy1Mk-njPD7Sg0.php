<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* @juyin/components/cards/product-card.html.twig */
class __TwigTemplate_c78bd8c8d7838aa5fc90bbb385b4d759 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
        $this->sandbox = $this->extensions[SandboxExtension::class];
        $this->checkSecurity();
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        // line 24
        yield "
";
        // line 26
        $context["variant"] = ((array_key_exists("variant", $context)) ? (Twig\Extension\CoreExtension::default(($context["variant"] ?? null), "default")) : ("default"));
        // line 27
        $context["show_actions"] = ((array_key_exists("show_actions", $context)) ? (Twig\Extension\CoreExtension::default(($context["show_actions"] ?? null), true)) : (true));
        // line 28
        $context["show_rating"] = ((array_key_exists("show_rating", $context)) ? (Twig\Extension\CoreExtension::default(($context["show_rating"] ?? null), true)) : (true));
        // line 29
        $context["show_description"] = ((array_key_exists("show_description", $context)) ? (Twig\Extension\CoreExtension::default(($context["show_description"] ?? null), true)) : (true));
        // line 30
        yield "
";
        // line 32
        $context["card_classes"] = ["default" => "bg-white rounded-lg shadow-soft overflow-hidden hover:shadow-medium transition-all duration-300 group", "compact" => "bg-white rounded-md shadow-sm overflow-hidden hover:shadow-md transition-all duration-200 group", "featured" => "bg-white rounded-xl shadow-medium overflow-hidden hover:shadow-strong transition-all duration-300 group border border-primary-100"];
        // line 37
        yield "
";
        // line 38
        $context["image_classes"] = ["default" => "aspect-w-1 aspect-h-1", "compact" => "aspect-w-4 aspect-h-3", "featured" => "aspect-w-1 aspect-h-1"];
        // line 43
        yield "
<article class=\"";
        // line 44
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, (($_v0 = ($context["card_classes"] ?? null)) && is_array($_v0) || $_v0 instanceof ArrayAccess && in_array($_v0::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v0[($context["variant"] ?? null)] ?? null) : CoreExtension::getAttribute($this->env, $this->source, ($context["card_classes"] ?? null), ($context["variant"] ?? null), [], "array", false, false, true, 44)), "html", null, true);
        yield "\" 
         data-product-id=\"";
        // line 45
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "id", [], "any", false, false, true, 45), "html", null, true);
        yield "\"
         itemscope 
         itemtype=\"https://schema.org/Product\">
  
  ";
        // line 50
        yield "  ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "image", [], "any", false, false, true, 50)) {
            // line 51
            yield "    <div class=\"";
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, (($_v1 = ($context["image_classes"] ?? null)) && is_array($_v1) || $_v1 instanceof ArrayAccess && in_array($_v1::class, CoreExtension::ARRAY_LIKE_CLASSES, true) ? ($_v1[($context["variant"] ?? null)] ?? null) : CoreExtension::getAttribute($this->env, $this->source, ($context["image_classes"] ?? null), ($context["variant"] ?? null), [], "array", false, false, true, 51)), "html", null, true);
            yield " bg-gray-100 relative overflow-hidden\">
      ";
            // line 52
            if (CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "image", [], "any", false, false, true, 52), "url", [], "any", false, false, true, 52)) {
                // line 53
                yield "        <img src=\"";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "image", [], "any", false, false, true, 53), "url", [], "any", false, false, true, 53), "html", null, true);
                yield "\" 
             alt=\"";
                // line 54
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, ((CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "image", [], "any", false, true, true, 54), "alt", [], "any", true, true, true, 54)) ? (Twig\Extension\CoreExtension::default(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "image", [], "any", false, false, true, 54), "alt", [], "any", false, false, true, 54), CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "title", [], "any", false, false, true, 54))) : (CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "title", [], "any", false, false, true, 54))), "html", null, true);
                yield "\"
             class=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"
             loading=\"lazy\"
             itemprop=\"image\">
      ";
            } else {
                // line 59
                yield "        ";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "image", [], "any", false, false, true, 59), "html", null, true);
                yield "
      ";
            }
            // line 61
            yield "      
      ";
            // line 63
            yield "      <div class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100\">
        <div class=\"flex space-x-2\">
          <button class=\"bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200 transform translate-y-2 group-hover:translate-y-0\"
                  aria-label=\"快速查看 ";
            // line 66
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "title", [], "any", false, false, true, 66), "html", null, true);
            yield "\"
                  data-action=\"quick-view\"
                  data-product-id=\"";
            // line 68
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "id", [], "any", false, false, true, 68), "html", null, true);
            yield "\">
            <svg class=\"w-5 h-5 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"/>
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"/>
            </svg>
          </button>
          <button class=\"bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200 transform translate-y-2 group-hover:translate-y-0\"
                  aria-label=\"收藏 ";
            // line 75
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "title", [], "any", false, false, true, 75), "html", null, true);
            yield "\"
                  data-action=\"favorite\"
                  data-product-id=\"";
            // line 77
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "id", [], "any", false, false, true, 77), "html", null, true);
            yield "\">
            <svg class=\"w-5 h-5 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"/>
            </svg>
          </button>
        </div>
      </div>

      ";
            // line 86
            yield "      ";
            if ((($context["variant"] ?? null) == "featured")) {
                // line 87
                yield "        <div class=\"absolute top-3 left-3\">
          <span class=\"bg-primary-600 text-white text-xs font-medium px-2 py-1 rounded-full\">
            精选
          </span>
        </div>
      ";
            }
            // line 93
            yield "    </div>
  ";
        }
        // line 95
        yield "
  ";
        // line 97
        yield "  <div class=\"p-4 ";
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar((((($context["variant"] ?? null) == "featured")) ? ("p-6") : ((((($context["variant"] ?? null) == "compact")) ? ("p-3") : ("p-4")))));
        yield "\">
    
    ";
        // line 100
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "brand", [], "any", false, false, true, 100)) {
            // line 101
            yield "      <div class=\"text-xs text-gray-500 uppercase tracking-wide mb-1\" itemprop=\"brand\">
        ";
            // line 102
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "brand", [], "any", false, false, true, 102), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 105
        yield "
    ";
        // line 107
        yield "    <h3 class=\"";
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar((((($context["variant"] ?? null) == "featured")) ? ("text-xl") : ("text-lg")));
        yield " font-semibold text-gray-900 mb-2 line-clamp-2\" itemprop=\"name\">
      <a href=\"";
        // line 108
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "url", [], "any", false, false, true, 108), "html", null, true);
        yield "\" 
         class=\"hover:text-primary-600 transition-colors duration-200\"
         itemprop=\"url\">
        ";
        // line 111
        yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "title", [], "any", false, false, true, 111), "html", null, true);
        yield "
      </a>
    </h3>

    ";
        // line 116
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "category", [], "any", false, false, true, 116)) {
            // line 117
            yield "      <div class=\"mb-2\">
        <span class=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\" itemprop=\"category\">
          ";
            // line 119
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "category", [], "any", false, false, true, 119), "html", null, true);
            yield "
        </span>
      </div>
    ";
        }
        // line 123
        yield "
    ";
        // line 125
        yield "    ";
        if (((($context["show_description"] ?? null) && CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "description", [], "any", false, false, true, 125)) && (($context["variant"] ?? null) != "compact"))) {
            // line 126
            yield "      <div class=\"text-gray-600 text-sm mb-3 line-clamp-2\" itemprop=\"description\">
        ";
            // line 127
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "description", [], "any", false, false, true, 127), "html", null, true);
            yield "
      </div>
    ";
        }
        // line 130
        yield "
    ";
        // line 132
        yield "    ";
        if ((($context["show_rating"] ?? null) && CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "rating", [], "any", false, false, true, 132))) {
            // line 133
            yield "      <div class=\"flex items-center mb-3\" itemprop=\"aggregateRating\" itemscope itemtype=\"https://schema.org/AggregateRating\">
        <div class=\"flex items-center\">
          ";
            // line 135
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(range(1, 5));
            foreach ($context['_seq'] as $context["_key"] => $context["i"]) {
                // line 136
                yield "            <svg class=\"w-3 h-3 ";
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar(((($context["i"] <= CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "rating", [], "any", false, false, true, 136))) ? ("text-yellow-400") : ("text-gray-300")));
                yield "\" 
                 fill=\"currentColor\" 
                 viewBox=\"0 0 20 20\" 
                 aria-hidden=\"true\">
              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"/>
            </svg>
          ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['i'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 143
            yield "        </div>
        <span class=\"ml-1 text-xs text-gray-500\">
          (<span itemprop=\"ratingValue\">";
            // line 145
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "rating", [], "any", false, false, true, 145), "html", null, true);
            yield "</span>)
        </span>
        <meta itemprop=\"bestRating\" content=\"5\">
        <meta itemprop=\"worstRating\" content=\"1\">
      </div>
    ";
        }
        // line 151
        yield "
    ";
        // line 153
        yield "    ";
        if (CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "price", [], "any", false, false, true, 153)) {
            // line 154
            yield "      <div class=\"mb-4\" itemprop=\"offers\" itemscope itemtype=\"https://schema.org/Offer\">
        <span class=\"";
            // line 155
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->renderVar((((($context["variant"] ?? null) == "featured")) ? ("text-xl") : ("text-lg")));
            yield " font-bold text-primary-600\" itemprop=\"price\">
          ¥";
            // line 156
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "price", [], "any", false, false, true, 156), "html", null, true);
            yield "
        </span>
        ";
            // line 158
            if ((CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "original_price", [], "any", false, false, true, 158) && (CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "original_price", [], "any", false, false, true, 158) != CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "price", [], "any", false, false, true, 158)))) {
                // line 159
                yield "          <span class=\"text-sm text-gray-500 line-through ml-2\">
            ¥";
                // line 160
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "original_price", [], "any", false, false, true, 160), "html", null, true);
                yield "
          </span>
        ";
            }
            // line 163
            yield "        <meta itemprop=\"priceCurrency\" content=\"CNY\">
        <meta itemprop=\"availability\" content=\"https://schema.org/InStock\">
      </div>
    ";
        }
        // line 167
        yield "
    ";
        // line 169
        yield "    ";
        if (($context["show_actions"] ?? null)) {
            // line 170
            yield "      <div class=\"flex space-x-2\">
        <a href=\"";
            // line 171
            yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "url", [], "any", false, false, true, 171), "html", null, true);
            yield "\" 
           class=\"flex-1 bg-primary-600 text-white text-center py-2 px-3 rounded-md hover:bg-primary-700 transition-colors duration-200 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\">
          查看详情
        </a>
        ";
            // line 175
            if ((($context["variant"] ?? null) != "compact")) {
                // line 176
                yield "          <button class=\"p-2 border border-gray-300 rounded-md hover:border-primary-600 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\"
                  aria-label=\"对比 ";
                // line 177
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "title", [], "any", false, false, true, 177), "html", null, true);
                yield "\"
                  data-action=\"compare\"
                  data-product-id=\"";
                // line 179
                yield $this->extensions['Drupal\Core\Template\TwigExtension']->escapeFilter($this->env, CoreExtension::getAttribute($this->env, $this->source, ($context["product"] ?? null), "id", [], "any", false, false, true, 179), "html", null, true);
                yield "\">
            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">
              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"/>
            </svg>
          </button>
        ";
            }
            // line 185
            yield "      </div>
    ";
        }
        // line 187
        yield "  </div>
</article>
";
        $this->env->getExtension('\Drupal\Core\Template\TwigExtension')
            ->checkDeprecations($context, ["product"]);        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "@juyin/components/cards/product-card.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  348 => 187,  344 => 185,  335 => 179,  330 => 177,  327 => 176,  325 => 175,  318 => 171,  315 => 170,  312 => 169,  309 => 167,  303 => 163,  297 => 160,  294 => 159,  292 => 158,  287 => 156,  283 => 155,  280 => 154,  277 => 153,  274 => 151,  265 => 145,  261 => 143,  247 => 136,  243 => 135,  239 => 133,  236 => 132,  233 => 130,  227 => 127,  224 => 126,  221 => 125,  218 => 123,  211 => 119,  207 => 117,  204 => 116,  197 => 111,  191 => 108,  186 => 107,  183 => 105,  177 => 102,  174 => 101,  171 => 100,  165 => 97,  162 => 95,  158 => 93,  150 => 87,  147 => 86,  136 => 77,  131 => 75,  121 => 68,  116 => 66,  111 => 63,  108 => 61,  102 => 59,  94 => 54,  89 => 53,  87 => 52,  82 => 51,  79 => 50,  72 => 45,  68 => 44,  65 => 43,  63 => 38,  60 => 37,  58 => 32,  55 => 30,  53 => 29,  51 => 28,  49 => 27,  47 => 26,  44 => 24,);
    }

    public function getSourceContext(): Source
    {
        return new Source("", "@juyin/components/cards/product-card.html.twig", "/Applications/XAMPP/xamppfiles/htdocs/drupal/themes/custom/juyin/templates/components/cards/product-card.html.twig");
    }
    
    public function checkSecurity()
    {
        static $tags = ["set" => 26, "if" => 50, "for" => 135];
        static $filters = ["default" => 26, "escape" => 44];
        static $functions = ["range" => 135];

        try {
            $this->sandbox->checkSecurity(
                ['set', 'if', 'for'],
                ['default', 'escape'],
                ['range'],
                $this->source
            );
        } catch (SecurityError $e) {
            $e->setSourceContext($this->source);

            if ($e instanceof SecurityNotAllowedTagError && isset($tags[$e->getTagName()])) {
                $e->setTemplateLine($tags[$e->getTagName()]);
            } elseif ($e instanceof SecurityNotAllowedFilterError && isset($filters[$e->getFilterName()])) {
                $e->setTemplateLine($filters[$e->getFilterName()]);
            } elseif ($e instanceof SecurityNotAllowedFunctionError && isset($functions[$e->getFunctionName()])) {
                $e->setTemplateLine($functions[$e->getFunctionName()]);
            }

            throw $e;
        }

    }
}
