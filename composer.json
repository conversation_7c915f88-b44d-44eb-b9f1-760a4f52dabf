{"name": "drupal/legacy-project", "description": "Project template for <PERSON><PERSON><PERSON> projects with composer following drupal/drupal layout", "type": "project", "license": "GPL-2.0-or-later", "homepage": "https://www.drupal.org/project/drupal", "support": {"docs": "https://www.drupal.org/docs/user_guide/en/index.html", "chat": "https://www.drupal.org/node/314178"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "require": {"composer/installers": "^2.0", "drupal/core-composer-scaffold": "^10.1", "drupal/core-project-message": "^10.1", "drupal/core-recommended": "^10.1", "drupal/core-vendor-hardening": "^10.1", "drupal/facets": "^3.0", "drupal/field_group": "^4.0", "drupal/metatag": "^2.1", "drupal/paragraphs": "^1.19", "drupal/pathauto": "^1.13", "drupal/schema_metatag": "^3.0", "drupal/search_api": "^1.38", "drupal/simple_sitemap": "^4.2", "drupal/webform": "^6.2", "drush/drush": "^13.6"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "stable", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "drupal/core-vendor-hardening": true, "phpstan/extension-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "sort-packages": true}, "extra": {"drupal-scaffold": {"locations": {"web-root": "./"}}, "installer-paths": {"core": ["type:drupal-core"], "libraries/{$name}": ["type:drupal-library"], "modules/contrib/{$name}": ["type:drupal-module"], "profiles/contrib/{$name}": ["type:drupal-profile"], "themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "modules/custom/{$name}": ["type:drupal-custom-module"], "profiles/custom/{$name}": ["type:drupal-custom-profile"], "themes/custom/{$name}": ["type:drupal-custom-theme"]}, "drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                         </>", "<bg=blue;fg=white>  Congratulations, you’ve installed the Drupal codebase  </>", "<bg=blue;fg=white>  from the drupal/legacy-project template!               </>", "<bg=blue;fg=white>                                                         </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Install the site: https://www.drupal.org/docs/installing-drupal", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://www.drupal.org/support", "  * Get involved with the Drupal community:", "      https://www.drupal.org/getting-involved", "  * Remove the plugin that prints this message:", "      composer remove drupal/core-project-message"]}}}