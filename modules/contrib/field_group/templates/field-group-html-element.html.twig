{#
/**
 * @file
 * Default theme implementation for a fieldgroup html element.
 *
 * Available variables:
 * - title: Title of the group.
 * - title_element: Element to wrap the title.
 * - children: The children of the group.
 * - wrapper_element: The html element to use
 * - attributes: A list of HTML attributes for the group wrapper.
 *
 * @see template_preprocess_field_group_html_element()
 *
 * @ingroup themeable
 */
#}

<{{ wrapper_element }} {{ attributes }}>
{% if title %}
  <{{ title_element }}{{ title_attributes }}>{{ title }}</{{ title_element }}>
{% endif %}
  {% if collapsible %}
<div class="field-group-wrapper">
  {% endif %}
  {{children}}
  {% if collapsible %}
</div>
{% endif %}
</{{ wrapper_element }}>