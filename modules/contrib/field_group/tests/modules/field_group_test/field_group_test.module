<?php

/**
 * @file
 * Fieldgroup test module.
 */

use <PERSON><PERSON>al\Core\Access\AccessResult;
use <PERSON>upal\Core\Field\FieldDefinitionInterface;
use <PERSON><PERSON>al\Core\Field\FieldItemListInterface;
use Drupal\Core\Session\AccountInterface;

/**
 * Implements hook_entity_field_access().
 */
function field_group_test_entity_field_access(
  $operation,
  FieldDefinitionInterface $field_definition,
  AccountInterface $account,
  ?FieldItemListInterface $items = NULL,
) {

  // Set access to false for field_no_access.
  if ($operation == 'view' && $field_definition->getName() == 'field_no_access') {
    return AccessResult::forbidden();
  }

  return AccessResult::neutral();

}
