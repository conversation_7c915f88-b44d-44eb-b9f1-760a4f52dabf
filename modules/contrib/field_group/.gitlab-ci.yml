################
# GitLabCI template for Drupal projects.
#
# This template is designed to give any Contrib maintainer everything they need to test, without requiring modification.
# It is also designed to keep up to date with Core Development automatically through the use of include files that can be centrally maintained.
# As long as you include the project, ref and three files below, any future updates added by the Drupal Association will be used in your
# pipelines automatically. However, you can modify this template if you have additional needs for your project.
# The full documentation is on https://project.pages.drupalcode.org/gitlab_templates/
################

# For information on alternative values for 'ref' see https://project.pages.drupalcode.org/gitlab_templates/info/templates-version/
# To test a Drupal 7 project, change the first include filename from .main.yml to .main-d7.yml
include:
  - project: $_GITLAB_TEMPLATES_REPO
    ref: $_GITLAB_TEMPLATES_REF
    file:
      - "/includes/include.drupalci.main.yml"
      - "/includes/include.drupalci.variables.yml"
      - "/includes/include.drupalci.workflows.yml"

################
# Pipeline configuration variables are defined with default values and descriptions in the file
# https://git.drupalcode.org/project/gitlab_templates/-/blob/main/includes/include.drupalci.variables.yml
# Uncomment the lines below if you want to override any of the variables. The following is just an example.
################
# variables:
#   SKIP_ESLINT: '1'
#   OPT_IN_TEST_NEXT_MAJOR: '1'
#   _CURL_TEMPLATES_REF: 'main'

################
# Field Group: Pipeline configuration variables and custom jobs, mostly:
#  - Composer: Require additional packages to pass phpstan validation.
#  - PHPCS and PHPStan: Require jobs to pass.
#  - Cspell: Ignore certain files or keywords.
#  - ESLint: Require eslint job to pass.
################

#
# Custom variables overrides.
#
variables:
  # CSpell: Ignore deprecated DrupalCI configuration file which should be
  # removed when DrupalCI is decommissioned in favor of GitlabCI.
  _CSPELL_IGNORE_PATHS: '"drupalci.yml"'

  # Opt-in to more tests.
  OPT_IN_TEST_MAX_PHP: '1'
  OPT_IN_TEST_NEXT_MAJOR: '1'

#
# Composer overrides and configuration.
#

# Require external packages dependencies to pass phpstan validation:
composer:
  after_script:
    - composer require drupal/ds
composer (next major):
  after_script:
    - composer require drupal/ds
