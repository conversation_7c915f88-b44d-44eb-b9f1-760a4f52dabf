services:
  plugin.manager.field_group.formatters:
    class: <PERSON><PERSON><PERSON>\field_group\FieldGroupFormatterPluginManager
    parent: default_plugin_manager
    arguments: ['@service_container']
  field_group.subscriber:
    class: <PERSON><PERSON>al\field_group\Routing\RouteSubscriber
    arguments: ['@entity_type.manager']
    tags:
      - { name: event_subscriber }
  field_group.param_converter:
    class: <PERSON><PERSON><PERSON>\field_group\Routing\FieldGroupConverter
    tags:
      - { name: paramconverter }
